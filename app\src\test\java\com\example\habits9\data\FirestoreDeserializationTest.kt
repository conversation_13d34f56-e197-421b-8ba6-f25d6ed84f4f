package com.example.habits9.data

import com.example.habits9.data.firestore.FirestoreHabit
import com.example.habits9.data.firestore.FirestoreConverters
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class to verify that the Firestore deserialization fix works correctly.
 * This specifically tests the fix for the FrequencyType deserialization issue where
 * Firestore was storing String but the code expected FrequencyType enum.
 */
class FirestoreDeserializationTest {

    @Test
    fun testFirestoreHabitWithListDaysOfWeekDeserialization() {
        // Simulate a FirestoreHabit object as it would come from Firestore
        // with daysOfWeek as List<Long> (which was causing the crash before)
        val firestoreHabit = FirestoreHabit(
            id = "test-habit-123",
            name = "Weekly Exercise",
            description = "Exercise routine",
            frequencyType = "WEEKLY",
            repeatsEvery = 1,
            daysOfWeek = listOf(1L, 3L, 5L) // Monday, Wednesday, Friday
        )

        // This should not throw an exception anymore
        val habit = FirestoreConverters.firestoreToHabit(firestoreHabit)

        // Verify the conversion worked correctly
        assertEquals("Weekly Exercise", habit.name)
        assertEquals("WEEKLY", habit.frequencyType)
        assertEquals(1, habit.repeatsEvery)
        assertEquals(listOf(1L, 3L, 5L), habit.daysOfWeek)
    }

    @Test
    fun testFirestoreHabitWithEmptyDaysOfWeekDeserialization() {
        // Test with empty list (daily habit)
        val firestoreHabit = FirestoreHabit(
            id = "daily-habit-456",
            name = "Daily Reading",
            description = "Read every day",
            frequencyType = "DAILY",
            repeatsEvery = 1,
            daysOfWeek = emptyList() // Empty list for daily habits
        )

        // This should work without issues
        val habit = FirestoreConverters.firestoreToHabit(firestoreHabit)

        // Verify the conversion worked correctly
        assertEquals("Daily Reading", habit.name)
        assertEquals("DAILY", habit.frequencyType)
        assertEquals(1, habit.repeatsEvery)
        assertEquals(emptyList<Long>(), habit.daysOfWeek)
    }

    @Test
    fun testHabitToFirestoreConversion() {
        // Test the reverse conversion - Habit to FirestoreHabit
        val habit = Habit(
            name = "Test Habit",
            description = "Test description",
            frequencyType = "WEEKLY",
            repeatsEvery = 2,
            daysOfWeek = listOf(2L, 4L, 6L) // Tuesday, Thursday, Saturday
        )

        val firestoreHabit = FirestoreConverters.habitToFirestore(habit)

        // Verify the conversion preserved the List<Long> format
        assertEquals("Test Habit", firestoreHabit.name)
        assertEquals("WEEKLY", firestoreHabit.frequencyType)
        assertEquals(2, firestoreHabit.repeatsEvery)
        assertEquals(listOf(2L, 4L, 6L), firestoreHabit.daysOfWeek)
    }

    @Test
    fun testRoundTripConversion() {
        // Test that we can convert Habit -> FirestoreHabit -> Habit without data loss
        val originalHabit = Habit(
            name = "Round Trip Test",
            description = "Testing round trip conversion",
            frequencyType = "WEEKLY",
            repeatsEvery = 1,
            daysOfWeek = listOf(1L, 7L) // Monday and Sunday
        )

        // Convert to Firestore format
        val firestoreHabit = FirestoreConverters.habitToFirestore(originalHabit)
        
        // Convert back to Habit
        val convertedHabit = FirestoreConverters.firestoreToHabit(firestoreHabit)

        // Verify no data was lost
        assertEquals(originalHabit.name, convertedHabit.name)
        assertEquals(originalHabit.description, convertedHabit.description)
        assertEquals(originalHabit.frequencyType, convertedHabit.frequencyType)
        assertEquals(originalHabit.repeatsEvery, convertedHabit.repeatsEvery)
        assertEquals(originalHabit.daysOfWeek, convertedHabit.daysOfWeek)
    }

    @Test
    fun testFrequencyTypeStringDeserialization() {
        // Test the specific issue from the error message: frequencyType as String
        val firestoreHabit = FirestoreHabit(
            id = "frequency-test-789",
            name = "Frequency Type Test",
            description = "Testing frequencyType String deserialization",
            frequencyType = "WEEKLY", // This should work as String
            repeatsEvery = 1,
            daysOfWeek = listOf(1L, 3L, 5L) // Monday, Wednesday, Friday
        )

        // This should not throw the "Can't convert String to FrequencyType" error
        val habit = FirestoreConverters.firestoreToHabit(firestoreHabit)

        // Verify the conversion worked correctly
        assertEquals("Frequency Type Test", habit.name)
        assertEquals("WEEKLY", habit.frequencyType) // Should remain as String
        assertEquals(1, habit.repeatsEvery)
        assertEquals(listOf(1L, 3L, 5L), habit.daysOfWeek)

        // Test the computed property for type safety
        assertEquals(FrequencyType.WEEKLY, habit.frequencyTypeEnum)
    }

    @Test
    fun testAllFrequencyTypesDeserialization() {
        // Test all frequency types to ensure they all work as Strings
        val frequencyTypes = listOf("DAILY", "WEEKLY", "MONTHLY")

        frequencyTypes.forEach { freqType ->
            val firestoreHabit = FirestoreHabit(
                id = "test-$freqType",
                name = "$freqType Habit",
                frequencyType = freqType,
                repeatsEvery = 1,
                daysOfWeek = emptyList()
            )

            // This should not throw any deserialization errors
            val habit = FirestoreConverters.firestoreToHabit(firestoreHabit)

            assertEquals(freqType, habit.frequencyType)
            assertEquals(FrequencyType.fromString(freqType), habit.frequencyTypeEnum)
        }
    }

    @Test
    fun testHabitTypeStringDeserialization() {
        // Test the specific fix for HabitType deserialization crash
        val firestoreHabitYesNo = FirestoreHabit(
            id = "habit-yes-no",
            name = "Yes/No Habit",
            type = "YES_NO" // String representation
        )

        val firestoreHabitNumerical = FirestoreHabit(
            id = "habit-numerical",
            name = "Numerical Habit",
            type = "NUMERICAL" // String representation
        )

        // These should not throw deserialization errors
        val habitYesNo = FirestoreConverters.firestoreToHabit(firestoreHabitYesNo)
        val habitNumerical = FirestoreConverters.firestoreToHabit(firestoreHabitNumerical)

        // Verify the conversion worked correctly
        assertEquals("YES_NO", habitYesNo.type)
        assertEquals(HabitType.YES_NO, habitYesNo.habitType)
        assertEquals(HabitType.YES_NO, habitYesNo.typeEnum)

        assertEquals("NUMERICAL", habitNumerical.type)
        assertEquals(HabitType.NUMERICAL, habitNumerical.habitType)
        assertEquals(HabitType.NUMERICAL, habitNumerical.typeEnum)
    }
}
