Please go through the above Things , Tell me if the above things can be implemented in a more efficient and better way. and suggest any improvements and modifications if needed first we will discuss about that . If everything is fine ,  Please generate an effective prompt to Work on all the above features and While generating the prompt Please Remember the note mentioned below also to include the below Mandatory Development Guidelines also :

Note : 
1. Right now developer has no capacity to read and understand images. Restraint from including any direct image references . Whatever we need that must be explained to the developer by text. 
2. always generate prompt in .md format !! this is efficiently understood by the developer . Strictly restrained from creating prompts in any other format. 

Mandatory Development Guidelines : 
1. Before implementing any new feature, the developer must always refer to the style guide for any styling-related queries or decisions. This ensures consistency and adherence to the project’s visual and structural standards. The style guide is located in the style.md file in the root folder, and it should be considered the single source of truth for all style-related questions.
2. Additionally, it is essential to first study the reference project before beginning any implementation. This helps the developer understand how similar features or workflows have been approached previously, ensuring alignment and reducing the risk of redundant or conflicting code. The reference project can be found in the uhabits-dev folder located in the root directory.
3. Before any code is written, the developer must take time to understand the current project structure, especially in relation to the new feature being developed , this is mandatory. Even if the feature is entirely new, there may be existing components, utilities, or foundational logic already present in the codebase. Understanding these connections is crucial to integrating the new feature cleanly and efficiently into the existing architecture.
4. Once the implementation is complete, the developer must ensure that the codebase is kept clean and organized. This involves removing any unnecessary routes, folders, files, or duplicate implementations that may have been created during the development process. Maintaining a clean codebase is vital for long-term scalability and to prevent confusion for future contributors.
5. Finally, if there is any confusion or lack of clarity regarding the feature requirements or implementation approach at any stage, the developer must stop immediately and seek clarification. This should be done either by consulting with the project lead or directly with me. It is better to resolve uncertainties upfront than to proceed with incorrect assumptions, which can lead to rework and wasted effort.
