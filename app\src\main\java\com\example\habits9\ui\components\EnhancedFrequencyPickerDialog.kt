package com.example.habits9.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import com.example.habits9.data.*

// Design System Colors - Dark Theme (following existing pattern)
private val DarkBackground = Color(0xFF121826)
private val SurfaceVariantDark = Color(0xFF1A202C)
private val TextPrimary = Color(0xFFE2E8F0)
private val TextSecondary = Color(0xFFA0AEC0)
private val AccentPrimary = Color(0xFF81E6D9)
private val DividerColor = Color(0xFF2D3748)
private val BackgroundDark = Color(0xFF121826)

@Composable
fun EnhancedFrequencyPickerDialog(
    currentFrequency: EnhancedFrequency,
    onFrequencySelected: (EnhancedFrequency) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedType by remember { mutableStateOf(currentFrequency.type) }
    var repeatsEvery by remember { mutableStateOf(currentFrequency.repeatsEvery.toString()) }
    var selectedDays by remember { mutableStateOf(currentFrequency.daysOfWeek.toSet()) }
    var dayOfMonth by remember { mutableStateOf(currentFrequency.dayOfMonth?.toString() ?: "1") }
    var weekOfMonth by remember { mutableStateOf(currentFrequency.weekOfMonth ?: 1) }
    var dayOfWeekInMonth by remember { mutableStateOf(currentFrequency.dayOfWeekInMonth ?: DayOfWeek.MONDAY) }
    
    // Create a temporary frequency to validate
    val tempFrequency = when (selectedType) {
        FrequencyType.DAILY -> EnhancedFrequency(
            type = FrequencyType.DAILY,
            repeatsEvery = repeatsEvery.toIntOrNull() ?: 0
        )
        FrequencyType.WEEKLY -> EnhancedFrequency(
            type = FrequencyType.WEEKLY,
            repeatsEvery = repeatsEvery.toIntOrNull() ?: 0,
            daysOfWeek = selectedDays.toList()
        )
        FrequencyType.MONTHLY -> EnhancedFrequency(
            type = FrequencyType.MONTHLY,
            repeatsEvery = repeatsEvery.toIntOrNull() ?: 0,
            dayOfMonth = dayOfMonth.toIntOrNull(),
            weekOfMonth = weekOfMonth,
            dayOfWeekInMonth = dayOfWeekInMonth
        )
    }

    val isValidInput = tempFrequency.isValid()
    val validationError = tempFrequency.getValidationError()
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Frequency",
                color = TextPrimary,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
            ) {
                // Frequency Type Tabs
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FrequencyType.values().forEach { type ->
                        FrequencyTypeTab(
                            type = type,
                            isSelected = selectedType == type,
                            onClick = { selectedType = type },
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Repeats Every Input
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Repeats every", color = TextPrimary, fontSize = 16.sp)
                    Spacer(modifier = Modifier.width(8.dp))
                    OutlinedTextField(
                        value = repeatsEvery,
                        onValueChange = { repeatsEvery = it },
                        modifier = Modifier.width(80.dp),
                        singleLine = true,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedTextColor = TextPrimary,
                            unfocusedTextColor = TextPrimary,
                            focusedBorderColor = AccentPrimary,
                            unfocusedBorderColor = DividerColor,
                            cursorColor = AccentPrimary
                        )
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when (selectedType) {
                            FrequencyType.DAILY -> if (repeatsEvery == "1") "day" else "days"
                            FrequencyType.WEEKLY -> if (repeatsEvery == "1") "week" else "weeks"
                            FrequencyType.MONTHLY -> if (repeatsEvery == "1") "month" else "months"
                        },
                        color = TextPrimary,
                        fontSize = 16.sp
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Type-specific options
                when (selectedType) {
                    FrequencyType.DAILY -> {
                        // No additional options for daily
                        Text(
                            text = "This habit will repeat every ${if (repeatsEvery == "1") "day" else "$repeatsEvery days"}",
                            color = TextSecondary,
                            fontSize = 14.sp
                        )
                    }
                    
                    FrequencyType.WEEKLY -> {
                        WeeklyOptions(
                            selectedDays = selectedDays,
                            onDaysChanged = { selectedDays = it }
                        )
                    }
                    
                    FrequencyType.MONTHLY -> {
                        MonthlyOptions(
                            dayOfMonth = dayOfMonth,
                            onDayOfMonthChanged = { dayOfMonth = it },
                            weekOfMonth = weekOfMonth,
                            dayOfWeekInMonth = dayOfWeekInMonth,
                            onWeekOfMonthChanged = { weekOfMonth = it },
                            onDayOfWeekInMonthChanged = { dayOfWeekInMonth = it }
                        )
                    }
                }

                // Show validation error if any
                validationError?.let { error ->
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                        fontSize = 12.sp,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (isValidInput) {
                        onFrequencySelected(tempFrequency)
                    }
                },
                enabled = isValidInput
            ) {
                Text("OK", color = if (isValidInput) AccentPrimary else TextSecondary)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel", color = TextSecondary)
            }
        },
        containerColor = SurfaceVariantDark
    )
}

@Composable
private fun FrequencyTypeTab(
    type: FrequencyType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(
                color = if (isSelected) AccentPrimary.copy(alpha = 0.2f) else SurfaceVariantDark
            )
            .clickable { onClick() }
            .padding(vertical = 8.dp, horizontal = 12.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = type.name.lowercase().replaceFirstChar { it.uppercase() },
            color = if (isSelected) AccentPrimary else TextSecondary,
            fontSize = 14.sp,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
        )
    }
}

@Composable
private fun WeeklyOptions(
    selectedDays: Set<DayOfWeek>,
    onDaysChanged: (Set<DayOfWeek>) -> Unit
) {
    Column {
        Text(
            text = "Select days of the week:",
            color = TextPrimary,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(DayOfWeek.values()) { day ->
                DayChip(
                    day = day,
                    isSelected = selectedDays.contains(day),
                    onClick = {
                        val newSelection = if (selectedDays.contains(day)) {
                            selectedDays - day
                        } else {
                            selectedDays + day
                        }
                        onDaysChanged(newSelection)
                    }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Quick selection buttons
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            TextButton(
                onClick = {
                    onDaysChanged(setOf(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY))
                }
            ) {
                Text("Weekdays", color = AccentPrimary, fontSize = 12.sp)
            }
            
            TextButton(
                onClick = {
                    onDaysChanged(setOf(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY))
                }
            ) {
                Text("Weekends", color = AccentPrimary, fontSize = 12.sp)
            }
            
            TextButton(
                onClick = {
                    onDaysChanged(DayOfWeek.values().toSet())
                }
            ) {
                Text("All", color = AccentPrimary, fontSize = 12.sp)
            }
        }
    }
}

@Composable
private fun DayChip(
    day: DayOfWeek,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(RoundedCornerShape(20.dp))
            .background(
                color = if (isSelected) AccentPrimary else SurfaceVariantDark
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = day.shortName.take(1),
            color = if (isSelected) BackgroundDark else TextPrimary,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun MonthlyOptions(
    dayOfMonth: String,
    onDayOfMonthChanged: (String) -> Unit,
    weekOfMonth: Int,
    dayOfWeekInMonth: DayOfWeek,
    onWeekOfMonthChanged: (Int) -> Unit,
    onDayOfWeekInMonthChanged: (DayOfWeek) -> Unit
) {
    Column {
        Text(
            text = "Monthly schedule:",
            color = TextPrimary,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Day of month option
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("On day", color = TextPrimary, fontSize = 16.sp)
            Spacer(modifier = Modifier.width(8.dp))
            OutlinedTextField(
                value = dayOfMonth,
                onValueChange = onDayOfMonthChanged,
                modifier = Modifier.width(80.dp),
                singleLine = true,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = TextPrimary,
                    unfocusedTextColor = TextPrimary,
                    focusedBorderColor = AccentPrimary,
                    unfocusedBorderColor = DividerColor,
                    cursorColor = AccentPrimary
                )
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("of the month", color = TextPrimary, fontSize = 16.sp)
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Advanced monthly patterns coming in future updates",
            color = TextSecondary,
            fontSize = 12.sp
        )
    }
}
