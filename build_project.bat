@echo off
echo Starting build process...

REM Check if JAVA_HOME is set
if "%JAVA_HOME%"=="" (
    echo ERROR: JAVA_HOME is not set!
    echo Please set JAVA_HOME to your JDK installation directory.
    echo Example: set JAVA_HOME=C:\Program Files\Java\jdk-11.0.x
    pause
    exit /b 1
)

echo Using JAVA_HOME: %JAVA_HOME%

REM Clean first
echo Running gradle clean...
call gradlew clean
if %ERRORLEVEL% neq 0 (
    echo Clean failed! Running cleanup script...
    call clean_build.bat
    echo Retrying clean...
    call gradlew clean
    if %ERRORLEVEL% neq 0 (
        echo Clean still failed! Please check your setup.
        pause
        exit /b 1
    )
)

REM Build the project
echo Running gradle build...
call gradlew build
if %ERRORLEVEL% neq 0 (
    echo Build failed! Check the error messages above.
    pause
    exit /b 1
)

echo Build completed successfully!
pause
