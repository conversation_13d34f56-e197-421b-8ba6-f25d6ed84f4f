# Please Follow the below debuging guidelines while resolving the error 


# Debug Process Guide

Please follow this exact structured debugging process to investigate and resolve the issue:


## 1. Understand the Error

- thoroughly understand the error context!
- Carefully interpret what the error says:
  - Understand the type of exception
  - Analyze the stack trace
  - Note any file paths or line numbers referenced

## 2. Trace the Error Location

- Navigate to all relevant files and lines mentioned in the error log
- Understand the surrounding code and the full implementation of the feature where the error occurred
- Focus on:
  - Control flow
  - Input/output
  - Any dependent components

## 3. Comprehend the Current Feature Implementation

- Develop a clear mental model of how the entire feature is structured and functions
- Identify how the involved files and methods interact
- Understand what they're intended to do

## 4. Determine the Root Cause

> **Important**: Before implementing any changes, it is mandatory to identify and finalize the true root cause of the issue.

Think deeply about potential causes:
- Logic error
- Missing configuration
- Incompatible encoding
- Race condition
- Misused library

**Clearly state the root cause once identified.**

## 5. Cross-Reference the Reference Project

- Once the root cause is finalized, examine the reference project at `./uhabits-dev`
- Compare relevant parts of the implementation
- Look for differences or proven approaches that could guide the solution

## 6. Plan and Execute the Fix

After gaining full understanding and validating it against the reference project, proceed to implement the fix with precision.

Ensure that:
1. The change is minimal and localized
2. It addresses the root cause directly
3. It does not introduce side effects

## 7. Verify

Test the fix thoroughly to ensure the issue is resolved.
