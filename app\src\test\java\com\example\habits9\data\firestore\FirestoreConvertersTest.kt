package com.example.habits9.data.firestore

import com.example.habits9.data.Habit
import com.example.habits9.data.Completion
import com.example.habits9.data.HabitSection
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for FirestoreConverters to ensure proper conversion between Room entities and Firestore models.
 */
class FirestoreConvertersTest {

    @Test
    fun `habitToFirestore converts Room Habit to FirestoreHabit correctly`() {
        // Given
        val habit = Habit(
            id = 123L,
            name = "Test Habit",
            description = "Test Description",
            creationDate = 1640995200000L, // Jan 1, 2022
            currentStreak = 5,
            completionDatesJson = """{"2022-01-01": true}""",
            uuid = "test-uuid-123",
            isArchived = false,
            position = 1,
            color = 8,
            type = "YES_NO",
            targetType = NumericalHabitType.AT_LEAST.value,
            targetValue = 0.0,
            unit = "",
            frequencyType = "DAILY",
            repeatsEvery = 1,
            daysOfWeek = emptyList(),
            dayOfMonth = null,
            weekOfMonth = null,
            dayOfWeekInMonth = null
        )

        // When
        val firestoreHabit = FirestoreConverters.habitToFirestore(habit)

        // Then
        assertEquals("123", firestoreHabit.id)
        assertEquals("Test Habit", firestoreHabit.name)
        assertEquals("Test Description", firestoreHabit.description)
        assertEquals(1640995200000L, firestoreHabit.creationDate)
        assertEquals(1640995200000L, firestoreHabit.createdAt) // Should be mapped from creationDate
        assertEquals(5, firestoreHabit.currentStreak)
        assertEquals("""{"2022-01-01": true}""", firestoreHabit.completionDatesJson)
        assertEquals("test-uuid-123", firestoreHabit.uuid)
        assertEquals(false, firestoreHabit.isArchived)
        assertEquals(1, firestoreHabit.position)
        assertEquals(8, firestoreHabit.color)
        assertEquals(HabitType.YES_NO.value, firestoreHabit.type)
        assertEquals(NumericalHabitType.AT_LEAST.value, firestoreHabit.targetType)
        assertEquals(0.0, firestoreHabit.targetValue, 0.001)
        assertEquals("", firestoreHabit.unit)
        assertEquals("DAILY", firestoreHabit.frequencyType)
        assertEquals(1, firestoreHabit.repeatsEvery)
        assertEquals(emptyList<Long>(), firestoreHabit.daysOfWeek)
        assertNull(firestoreHabit.dayOfMonth)
        assertNull(firestoreHabit.weekOfMonth)
        assertNull(firestoreHabit.dayOfWeekInMonth)
    }

    @Test
    fun `firestoreToHabit converts FirestoreHabit to Room Habit correctly`() {
        // Given
        val firestoreHabit = FirestoreHabit(
            id = "456",
            name = "Firestore Habit",
            description = "Firestore Description",
            creationDate = 1640995200000L,
            createdAt = 1641081600000L, // Jan 2, 2022 - different from creationDate to test priority
            currentStreak = 3,
            completionDatesJson = """{"2022-01-02": true}""",
            uuid = "firestore-uuid-456",
            isArchived = true,
            position = 2,
            color = 5,
            type = "NUMERICAL",
            targetType = NumericalHabitType.AT_MOST.value,
            targetValue = 10.0,
            unit = "glasses",
            frequencyType = "WEEKLY",
            repeatsEvery = 2,
            daysOfWeek = listOf(1L, 3L, 5L),
            dayOfMonth = 15,
            weekOfMonth = 2,
            dayOfWeekInMonth = 3
        )

        // When
        val habit = FirestoreConverters.firestoreToHabit(firestoreHabit)

        // Then
        assertEquals(456L, habit.id)
        assertEquals("Firestore Habit", habit.name)
        assertEquals("Firestore Description", habit.description)
        assertEquals(1641081600000L, habit.creationDate) // Should use createdAt when available
        assertEquals(3, habit.currentStreak)
        assertEquals("""{"2022-01-02": true}""", habit.completionDatesJson)
        assertEquals("firestore-uuid-456", habit.uuid)
        assertEquals(true, habit.isArchived)
        assertEquals(2, habit.position)
        assertEquals(5, habit.color)
        assertEquals(HabitType.NUMERICAL.value, habit.type)
        assertEquals(NumericalHabitType.AT_MOST.value, habit.targetType)
        assertEquals(10.0, habit.targetValue, 0.001)
        assertEquals("glasses", habit.unit)
        assertEquals("WEEKLY", habit.frequencyType)
        assertEquals(2, habit.repeatsEvery)
        assertEquals(listOf(1L, 3L, 5L), habit.daysOfWeek)
        assertEquals(15, habit.dayOfMonth)
        assertEquals(2, habit.weekOfMonth)
        assertEquals(3, habit.dayOfWeekInMonth)
    }

    @Test
    fun `completionToFirestore converts Room Completion to FirestoreCompletion correctly`() {
        // Given
        val completion = Completion(
            id = "789",
            habitId = 123L,
            timestamp = 1640995200000L,
            value = "5.0"
        )
        val habitDocumentId = "habit-doc-123"

        // When
        val firestoreCompletion = FirestoreConverters.completionToFirestore(completion, habitDocumentId)

        // Then
        assertEquals("789", firestoreCompletion.id)
        assertEquals("habit-doc-123", firestoreCompletion.habitId)
        assertEquals(1640995200000L, firestoreCompletion.timestamp)
        assertEquals("5.0", firestoreCompletion.value)
    }

    @Test
    fun `firestoreToCompletion converts FirestoreCompletion to Room Completion correctly`() {
        // Given
        val firestoreCompletion = FirestoreCompletion(
            id = "987",
            habitId = "habit-doc-456",
            timestamp = 1640995200000L,
            value = "3.5"
        )
        val roomHabitId = 456L

        // When
        val completion = FirestoreConverters.firestoreToCompletion(firestoreCompletion, roomHabitId)

        // Then
        assertEquals("987", completion.id)
        assertEquals(456L, completion.habitId)
        assertEquals(1640995200000L, completion.timestamp)
        assertEquals("3.5", completion.value)
    }

    @Test
    fun `habitSectionToFirestore converts HabitSection to FirestoreHabitSection correctly`() {
        // Given
        val habitSection = HabitSection(
            id = "section_123",
            name = "Work",
            color = 0xFF0000FF.toInt(),
            displayOrder = 0
        )

        // When
        val firestoreHabitSection = FirestoreConverters.habitSectionToFirestore(habitSection)

        // Then
        assertEquals("section_123", firestoreHabitSection.id)
        assertEquals("Work", firestoreHabitSection.name)
        assertEquals(0xFF0000FF.toInt(), firestoreHabitSection.color)
        assertEquals(0, firestoreHabitSection.displayOrder)
    }

    @Test
    fun `firestoreToHabitSection converts FirestoreHabitSection to HabitSection correctly`() {
        // Given
        val firestoreHabitSection = FirestoreHabitSection(
            id = "section_456",
            name = "Personal",
            color = 0xFF00FF00.toInt(),
            displayOrder = 1
        )

        // When
        val habitSection = FirestoreConverters.firestoreToHabitSection(firestoreHabitSection)

        // Then
        assertEquals("section_456", habitSection.id)
        assertEquals("Personal", habitSection.name)
        assertEquals(0xFF00FF00.toInt(), habitSection.color)
        assertEquals(1, habitSection.displayOrder)
    }

    @Test
    fun `habitToFirestore handles new habit with id 0 correctly`() {
        // Given
        val newHabit = Habit(
            id = 0L, // New habit
            name = "New Habit",
            description = "New Description"
        )

        // When
        val firestoreHabit = FirestoreConverters.habitToFirestore(newHabit)

        // Then
        assertEquals("", firestoreHabit.id) // Should be empty for new habits
        assertEquals("New Habit", firestoreHabit.name)
        assertEquals("New Description", firestoreHabit.description)
    }

    @Test
    fun `completionToFirestore handles new completion with empty id correctly`() {
        // Given
        val newCompletion = Completion(
            id = "", // New completion
            habitId = 123L,
            timestamp = 1640995200000L,
            value = null
        )
        val habitDocumentId = "habit-doc-123"

        // When
        val firestoreCompletion = FirestoreConverters.completionToFirestore(newCompletion, habitDocumentId)

        // Then
        assertEquals("", firestoreCompletion.id) // Should be empty for new completions
        assertEquals("habit-doc-123", firestoreCompletion.habitId)
        assertEquals(1640995200000L, firestoreCompletion.timestamp)
        assertNull(firestoreCompletion.value)
    }

    @Test
    fun `habitSectionToFirestore handles new section with empty id correctly`() {
        // Given
        val newSection = HabitSection(
            id = "", // New section
            name = "New Section",
            color = 0xFF000000.toInt(),
            displayOrder = 0
        )

        // When
        val firestoreHabitSection = FirestoreConverters.habitSectionToFirestore(newSection)

        // Then
        assertEquals("", firestoreHabitSection.id) // Should be empty for new sections
        assertEquals("New Section", firestoreHabitSection.name)
        assertEquals(0xFF000000.toInt(), firestoreHabitSection.color)
        assertEquals(0, firestoreHabitSection.displayOrder)
    }

    @Test
    fun `firestoreToHabit uses creationDate when createdAt is 0`() {
        // Given
        val firestoreHabit = FirestoreHabit(
            id = "789",
            name = "Legacy Habit",
            description = "Legacy Description",
            creationDate = 1640995200000L, // Jan 1, 2022
            createdAt = 0L, // Default value - should fall back to creationDate
            currentStreak = 1,
            completionDatesJson = "{}",
            uuid = "legacy-uuid-789",
            isArchived = false,
            position = 0,
            color = 8,
            type = "YES_NO",
            targetType = 0,
            targetValue = 0.0,
            unit = "",
            frequencyType = "DAILY",
            repeatsEvery = 1,
            daysOfWeek = emptyList(),
            dayOfMonth = null,
            weekOfMonth = null,
            dayOfWeekInMonth = null
        )

        // When
        val habit = FirestoreConverters.firestoreToHabit(firestoreHabit)

        // Then
        assertEquals(1640995200000L, habit.creationDate) // Should use creationDate when createdAt is 0
    }
}
