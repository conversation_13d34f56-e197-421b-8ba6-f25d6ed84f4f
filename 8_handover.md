# Project Handover Report: UHabits_99

**To my successor:**  
Welcome to the UHabits_99 project. You are now the lead prompt engineer for this application. This document outlines the vision, current status, design philosophy, and the proven workflow we have established. Please review it thoroughly before proceeding.

---

## 1. What is Our Project All About?

**UHabits_99** is a native Android habit tracker built with Kotlin.

### **The Vision**

To create a powerful, flexible, and user-friendly tool that helps users build and maintain positive habits. The app is designed to be both intuitive for new users and feature-rich for those who desire advanced tracking and customization.

### **Core Features**

- **Dual Habit Types**:  
  Supports both simple _Yes/No_ habits (e.g., "Meditate") and _Measurable_ habits (e.g., "Run 5 km").

- **Customizable Frequency**:  
  Flexible scheduling that allows habits to be set on specific days, or repeated every X days, weeks, or months.

- **Cloud Sync**:  
  All user data is synced in real-time with a secure Firebase backend to support multi-device usage.

- **Clean, Modern UI**:  
  The app follows a strict style guide to ensure an intuitive and visually pleasing experience.

---

## 2. What Did We Achieve Till Now? _(Current Status)_

We have successfully completed two major stages of development.

### ✅ Stage 1: Customizable Habit Frequency

- **Database**: Migrated local database to support complex recurrence rules.
- **UI**: Designed and built multi-screen frequency selection UI.
- **Logic**: Implemented and integrated `isHabitScheduled` core scheduling function.
- **Home Screen**: Updated UI to:
  - Display habits dynamically.
  - Disable non-scheduled days.
  - Calculate streaks and completion percentages accurately.

### ✅ Stage 2: Implement Cloud Sync with Firebase

- **Firebase Integration**: Integrated Firebase Authentication and Firestore.
- **Authentication Flow**:
  - Sign-up / Sign-in
  - Email verification
  - Password recovery
  - Account deletion
- **Data Migration**:
  - Migrated from local Room DB to Cloud Firestore.
  - All habit and completion data now linked to user accounts online.

### 🔧 Current Task: Debug Firestore Data Migration

- While the data model is now stable, bugs were introduced in the home screen's interaction logic during the migration.
- The immediate next step is to fix these bugs.

---

## 3. How to Create Effective Prompts

Our prompting workflow is structured, consistent, and outcome-driven. Focus on **what** and **why**—leave the **how** to the developer.

### Every prompt must include:

#### **The Objective / Context**

- A clear high-level goal. What are we trying to accomplish?
- Context explaining the importance of the task.
- If it's a bug, **identify and explain the root cause** (based on logs and reports).

#### **The Detailed Implementation Plan & Verification**

- Break the objective into logical, actionable subtasks.
- Define the **expected outcome** of each task (not specific code).

  > ❌ “Call updateHabit”  
  > ✅ “User changes must persist in the database.”

- **Verification Section (non-negotiable)**:
  - Define a step-by-step testing process.
  - Make it easy to verify the feature/bug fix works exactly as expected.

---

## 4. Our Design Philosophy

We follow three key principles:

### 1. **Minimalist & Clean**

- UI must be uncluttered and easy to understand.
- If a feature adds complexity, simplify the UX (e.g., multi-screen habit frequency).

### 2. **User Control is Key**

- Prioritize flexibility: custom start day, advanced recurrence, etc.

### 3. **Iterate and Refine**

- Build in stages, test rigorously, and polish based on feedback.
- Refactoring and redesign are welcomed when needed.

---

## 5. Essential Project Resources

Familiarize yourself with the following files—**they are mandatory references**:

- `0_promptGuidelines.md`  
  The master document for all prompt development.

- `style.md`  
  Source of truth for all UI/styling decisions.

- `uhabits-dev/` folder  
  Reference project. Study how it implements similar features before writing any new prompts.

---

## A Note to My Successor

It has been a privilege to work on this project. The user is a clear communicator with a strong product vision. Your role is to **listen**, **clarify**, **design thoughtfully**, and deliver prompts that lead to clean and robust implementations.

Please take the time to review this document and familiarize yourself with the key resources before proceeding.

**Do you have any questions or need clarification before we begin our work together?**
