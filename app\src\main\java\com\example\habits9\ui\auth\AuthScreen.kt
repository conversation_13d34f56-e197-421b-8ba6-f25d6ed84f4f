package com.example.habits9.ui.auth

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.AlertDialog
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel

// Design System Colors (from style.md)
private val LightBackground = Color(0xFFFFFFFF)
private val LightBackgroundDarker = Color(0xFFF7FAFC)
private val LightSurfaceVariant = Color(0xFFFFFFFF)
private val LightTextPrimary = Color(0xFF2D3748)
private val LightTextSecondary = Color(0xFF718096)
private val LightAccentPrimary = Color(0xFF38B2AC)
private val LightAccentPrimaryLogo = Color(0xFF64C5BF)
private val LightIconColor = Color(0xFFA0AEC0)
private val LightError = Color(0xFFE53E3E)

private val DarkBackground = Color(0xFF121826)
private val DarkBackgroundDarker = Color(0xFF1A202C)
private val DarkSurfaceVariant = Color(0xFF1F2937)
private val DarkTextPrimary = Color(0xFFE2E8F0)
private val DarkTextSecondary = Color(0xFFA0AEC0)
private val DarkAccentPrimary = Color(0xFF81E6D9)
private val DarkAccentPrimaryLogo = Color(0xFFA8F0E5)
private val DarkIconColor = Color(0xFF9CA3AF)

@Composable
fun AuthScreen(
    onNavigateToHome: () -> Unit = {},
    onNavigateToVerification: (String) -> Unit = {},
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val uiState by authViewModel.uiState.collectAsState()
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    var showForgotPasswordDialog by remember { mutableStateOf(false) }
    val isSignIn = selectedTabIndex == 0

    // Use system theme colors but with our design system
    val isDarkTheme = MaterialTheme.colorScheme.background == Color(0xFF121826) ||
                     MaterialTheme.colorScheme.background.red < 0.5f

    val backgroundColor = if (isDarkTheme) DarkBackground else LightBackground
    val backgroundDarker = if (isDarkTheme) DarkBackgroundDarker else LightBackgroundDarker
    val surfaceVariant = if (isDarkTheme) DarkSurfaceVariant else LightSurfaceVariant
    val textPrimary = if (isDarkTheme) DarkTextPrimary else LightTextPrimary
    val textSecondary = if (isDarkTheme) DarkTextSecondary else LightTextSecondary
    val accentPrimary = if (isDarkTheme) DarkAccentPrimary else LightAccentPrimary
    val accentPrimaryLogo = if (isDarkTheme) DarkAccentPrimaryLogo else LightAccentPrimaryLogo
    val iconColor = if (isDarkTheme) DarkIconColor else LightIconColor
    val errorColor = LightError

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(backgroundColor, backgroundDarker)
                )
            )
            .padding(horizontal = 20.dp), // viewport spacing from style guide
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(24.dp) // increased spacing
        ) {
            // App Logo and Title
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = "UHabits Logo",
                modifier = Modifier.size(64.dp),
                tint = accentPrimaryLogo
            )

            Text(
                text = "UHabits",
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 24.sp
                ),
                color = textPrimary,
                textAlign = TextAlign.Center
            )

            Text(
                text = "Build Better Habits, Together",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp
                ),
                color = textPrimary,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Tab Row for Sign In / Sign Up
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.fillMaxWidth(),
                containerColor = Color.Transparent,
                contentColor = accentPrimary,
                indicator = { tabPositions ->
                    TabRowDefaults.Indicator(
                        modifier = Modifier.tabIndicatorOffset(tabPositions[selectedTabIndex]),
                        color = accentPrimary,
                        height = 2.dp
                    )
                }
            ) {
                Tab(
                    selected = selectedTabIndex == 0,
                    onClick = { selectedTabIndex = 0 },
                    text = {
                        Text(
                            text = "Sign In",
                            color = if (selectedTabIndex == 0) accentPrimary else textSecondary,
                            fontWeight = if (selectedTabIndex == 0) FontWeight.Medium else FontWeight.Normal
                        )
                    }
                )
                Tab(
                    selected = selectedTabIndex == 1,
                    onClick = { selectedTabIndex = 1 },
                    text = {
                        Text(
                            text = "Sign Up",
                            color = if (selectedTabIndex == 1) accentPrimary else textSecondary,
                            fontWeight = if (selectedTabIndex == 1) FontWeight.Medium else FontWeight.Normal
                        )
                    }
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Email Input Field
            OutlinedTextField(
                value = uiState.email,
                onValueChange = authViewModel::updateEmail,
                placeholder = {
                    Text(
                        "Email",
                        style = MaterialTheme.typography.bodyMedium,
                        color = textSecondary
                    )
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Email,
                        contentDescription = "Email",
                        tint = iconColor
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(24.dp)),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                singleLine = true,
                enabled = !uiState.isLoading,
                shape = RoundedCornerShape(24.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = accentPrimary,
                    unfocusedBorderColor = Color.Transparent,
                    focusedContainerColor = surfaceVariant,
                    unfocusedContainerColor = surfaceVariant,
                    focusedTextColor = textPrimary,
                    unfocusedTextColor = textPrimary
                )
            )

            // Password Input Field
            OutlinedTextField(
                value = uiState.password,
                onValueChange = authViewModel::updatePassword,
                placeholder = {
                    Text(
                        "Password",
                        style = MaterialTheme.typography.bodyMedium,
                        color = textSecondary
                    )
                },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Lock,
                        contentDescription = "Password",
                        tint = iconColor
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(24.dp)),
                visualTransformation = PasswordVisualTransformation(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                singleLine = true,
                enabled = !uiState.isLoading,
                shape = RoundedCornerShape(24.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = accentPrimary,
                    unfocusedBorderColor = Color.Transparent,
                    focusedContainerColor = surfaceVariant,
                    unfocusedContainerColor = surfaceVariant,
                    focusedTextColor = textPrimary,
                    unfocusedTextColor = textPrimary
                )
            )

            // Error Message
            if (uiState.errorMessage.isNotEmpty()) {
                Text(
                    text = uiState.errorMessage,
                    color = errorColor,
                    style = MaterialTheme.typography.bodySmall.copy(fontSize = 12.sp),
                    textAlign = TextAlign.Start,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Forgot Password Link (only show on Sign In tab)
            if (isSignIn) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = { showForgotPasswordDialog = true },
                        enabled = !uiState.isLoading
                    ) {
                        Text(
                            text = "Forgot Password?",
                            color = accentPrimary,
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontWeight = FontWeight.Medium
                            )
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Action Button (Sign In or Sign Up based on selected tab)
            Button(
                onClick = {
                    if (isSignIn) {
                        authViewModel.signIn(onSuccess = onNavigateToHome)
                    } else {
                        authViewModel.signUp(onSuccess = { email ->
                            onNavigateToVerification(email)
                        })
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .clip(RoundedCornerShape(24.dp)),
                enabled = !uiState.isLoading,
                colors = ButtonDefaults.buttonColors(
                    containerColor = accentPrimary,
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(24.dp)
            ) {
                if (uiState.isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text(
                        text = if (isSignIn) "Sign In" else "Sign Up",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Medium,
                            fontSize = 14.sp
                        )
                    )
                }
            }

            // Toggle text for switching between Sign In and Sign Up
            if (!isSignIn) {
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "Already have an account? ",
                        color = textSecondary,
                        style = MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = "Sign In",
                        color = accentPrimary,
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        modifier = Modifier.clickable { selectedTabIndex = 0 }
                    )
                }
            }
        }
    }

    // Forgot Password Dialog
    if (showForgotPasswordDialog) {
        ForgotPasswordDialog(
            onDismiss = { showForgotPasswordDialog = false },
            onSendResetEmail = { email ->
                authViewModel.sendPasswordResetEmail(
                    email = email,
                    onSuccess = {
                        showForgotPasswordDialog = false
                        // Show success message (could be a toast/snackbar)
                    },
                    onError = { error ->
                        // Handle error (could show in dialog or as toast)
                    }
                )
            },
            textPrimary = textPrimary,
            textSecondary = textSecondary,
            accentPrimary = accentPrimary,
            surfaceVariant = surfaceVariant,
            iconColor = iconColor
        )
    }
}

@Composable
private fun ForgotPasswordDialog(
    onDismiss: () -> Unit,
    onSendResetEmail: (String) -> Unit,
    textPrimary: Color,
    textSecondary: Color,
    accentPrimary: Color,
    surfaceVariant: Color,
    iconColor: Color
) {
    var email by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Reset Password",
                color = textPrimary,
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Medium
                )
            )
        },
        text = {
            Column {
                Text(
                    text = "Enter your email address and we'll send you a link to reset your password.",
                    color = textSecondary,
                    style = MaterialTheme.typography.bodyMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    placeholder = {
                        Text(
                            "Email",
                            style = MaterialTheme.typography.bodyMedium,
                            color = textSecondary
                        )
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Email,
                            contentDescription = "Email",
                            tint = iconColor
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(12.dp)),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                    singleLine = true,
                    enabled = !isLoading,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = accentPrimary,
                        unfocusedBorderColor = Color.Transparent,
                        focusedContainerColor = surfaceVariant,
                        unfocusedContainerColor = surfaceVariant,
                        focusedTextColor = textPrimary,
                        unfocusedTextColor = textPrimary
                    )
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (email.isNotBlank()) {
                        isLoading = true
                        onSendResetEmail(email)
                    }
                },
                enabled = !isLoading && email.isNotBlank(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = accentPrimary,
                    contentColor = Color.White
                )
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("Send Reset Email")
                }
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text(
                    "Cancel",
                    color = textSecondary
                )
            }
        }
    )
}
