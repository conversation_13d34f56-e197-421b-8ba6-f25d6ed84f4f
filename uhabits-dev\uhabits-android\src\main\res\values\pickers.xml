<?xml version="1.0" encoding="utf-8" ?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <dimen name="color_swatch_large">64dip</dimen>
    <dimen name="color_swatch_small">48dip</dimen>
    <dimen name="color_swatch_margins_large">8dip</dimen>
    <dimen name="color_swatch_margins_small">4dip</dimen>

    <item name="circle_radius_multiplier" format="float" translatable="false" type="string">
        0.82
    </item>
    <item name="circle_radius_multiplier_24HourMode" format="float" translatable="false" type="string">
        0.85
    </item>
    <item name="selection_radius_multiplier" format="float" translatable="false" type="string">
        0.16
    </item>
    <item name="ampm_circle_radius_multiplier" format="float" translatable="false" type="string">
        0.19
    </item>
    <item name="numbers_radius_multiplier_normal" format="float" translatable="false" type="string">
        0.81
    </item>
    <item name="numbers_radius_multiplier_inner" format="float" translatable="false" type="string">
        0.60
    </item>
    <item name="numbers_radius_multiplier_outer" format="float" translatable="false" type="string">
        0.83
    </item>
    <item name="text_size_multiplier_normal" format="float" translatable="false" type="string">
        0.17
    </item>
    <item name="text_size_multiplier_inner" format="float" translatable="false" type="string">
        0.14
    </item>
    <item name="text_size_multiplier_outer" format="float" translatable="false" type="string">
        0.11
    </item>

    <dimen name="time_label_size">60sp</dimen>
    <dimen name="extra_time_label_margin">-30dp</dimen>
    <dimen name="ampm_label_size">16sp</dimen>
    <dimen name="done_label_size">14sp</dimen>
    <dimen name="ampm_left_padding">6dip</dimen>
    <dimen name="separator_padding">4dip</dimen>
    <dimen name="header_height">80dip</dimen>
    <dimen name="footer_height">48dip</dimen>
    <dimen name="minimum_margin_sides">48dip</dimen>
    <dimen name="minimum_margin_top_bottom">24dip</dimen>
    <dimen name="picker_dimen">250dip</dimen>
    <dimen name="date_picker_component_width">250dp</dimen>
    <dimen name="date_picker_header_height">30dp</dimen>
    <dimen name="selected_calendar_layout_height">155dp</dimen>
    <dimen name="date_picker_view_animator_height">270dp</dimen>
    <dimen name="done_button_height">42dp</dimen>
    <dimen name="month_list_item_header_height">50dp</dimen>
    <dimen name="month_day_label_text_size">10sp</dimen>
    <dimen name="day_number_select_circle_radius">16dp</dimen>
    <dimen name="month_select_circle_radius">45dp</dimen>
    <dimen name="selected_date_year_size">30dp</dimen>
    <dimen name="selected_date_day_size">75dp</dimen>
    <dimen name="selected_date_month_size">30dp</dimen>
    <dimen name="date_picker_header_text_size">14dp</dimen>
    <dimen name="month_label_size">16sp</dimen>
    <dimen name="day_number_size">16sp</dimen>
    <dimen name="year_label_height">64dp</dimen>
    <dimen name="year_label_text_size">22dp</dimen>

    <string name="color_swatch_description" translatable="false">Color <xliff:g example="14" id="color_index">%1$d</xliff:g></string>
    <string name="color_swatch_description_selected" translatable="false">Color <xliff:g example="14" id="color_index">%1$d</xliff:g> selected</string>

    <!-- Date and time picker -->
    <string name="hour_picker_description" translatable="false">Hours circular slider</string>
    <string name="minute_picker_description" translatable="false">Minutes circular slider</string>
    <string name="day_picker_description" translatable="false">Month grid of days</string>
    <string name="year_picker_description" translatable="false">Year list</string>
    <string name="select_day" translatable="false">Select month and day</string>
    <string name="select_year" translatable="false">Select year</string>
    <string name="item_is_selected" translatable="false"><xliff:g example="2013" id="item">%1$s</xliff:g> selected</string>
    <string name="deleted_key" translatable="false"><xliff:g example="4" id="key">%1$s</xliff:g> deleted</string>
    <string name="time_placeholder" translatable="false">--</string>
    <string name="time_separator" translatable="false">:</string>
    <string name="radial_numbers_typeface" translatable="false">sans-serif</string>
    <string name="sans_serif" translatable="false">sans-serif</string>
    <string name="day_of_week_label_typeface" translatable="false">sans-serif</string>

    <!-- Time and Date picker -->
    <color name="circle_background">#f2f2f2</color>
    <color name="line_background">#cccccc</color>
    <color name="ampm_text_color">#8c8c8c</color>
    <color name="done_text_color_normal">#000000</color>
    <color name="done_text_color_disabled">#cccccc</color>
    <color name="numbers_text_color">#8c8c8c</color>
    <color name="transparent">#00000000</color>
    <color name="transparent_black">#7f000000</color>
    <color name="blue">#33b5e5</color>
    <color name="blue_focused">#c1e8f7</color>
    <color name="neutral_pressed">#33999999</color>
    <color name="darker_blue">#0099cc</color>
    <color name="date_picker_text_normal">#ff999999</color>
    <color name="calendar_header">#999999</color>
    <color name="date_picker_view_animator">#f2f2f2</color>
    <color name="calendar_selected_date_text">#ffd1d2d4</color>
    <color name="done_text_color">#888888</color>
    <color name="done_text_color_dark">#888888</color>
<!--    <color name="white">#ffffff</color>-->
<!--    <color name="black">#000000</color>-->

    <!-- Colors for red theme -->
    <color name="red">#ff3333</color>
    <color name="red_focused">#853333</color>
    <color name="light_gray">#404040</color>
    <color name="dark_gray">#363636</color>
    <color name="line_dark">#808080</color>

    <style name="time_label">
        <item name="android:textSize">@dimen/time_label_size</item>
        <item name="android:textColor">@color/numbers_text_color</item>
    </style>

    <style name="ampm_label">
        <item name="android:textSize">@dimen/ampm_label_size</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">@color/ampm_text_color</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TimePickerDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="windowNoTitle">true</item>
    </style>

<!--    <string name="select_hours" translatable="false"/>-->
<!--    <string name="select_minutes" translatable="false"/>-->
<!--    <string name="color_picker_default_title" translatable="false"/>-->
<!--    <string name="clear" translatable="false"/>-->
<!--    <string name="clear_label" translatable="false"/>-->
<!--    <string name="done_label" translatable="false"/>-->
</resources>