# Debug Script for Habit Tracker Issues

## Issues to Debug

1. **Yes/No Habit Cross-Completion**: When marking one Yes/No habit as complete, all other habits for that day are also getting marked as done.
2. **Measurable Habit Click Issue**: Clicking on measurable habits doesn't trigger the numerical input dialog.

## Debug Steps

### Step 1: Check Habit Types in Database
- Verify that habits are correctly stored with their types (YES_NO vs NUMERICAL)
- Check if habit type conversion is working correctly

### Step 2: Test Yes/No Habit Completion
1. Click on a Yes/No habit for a specific day
2. Check logs for:
   - UI click detection
   - ViewModel method call
   - Database operation
   - State update
   - UI re-render

### Step 3: Test Measurable Habit Click
1. Click on a measurable habit for a specific day
2. Check logs for:
   - UI click detection
   - Habit type verification
   - Dialog state update
   - Dialog rendering

### Step 4: Verify State Management
- Check if completion state is properly isolated by habit ID
- Verify that dialog state is properly propagated to UI

## Expected Log Flow

### For Yes/No Habits:
```
BugFix: UI Clicked - Habit: [HabitName], DayStart: [timestamp], isScheduled: true
BugFix: Habit details - ID: [habitId], Type: YES_NO, HabitType: YES_NO
BugFix: YES_NO habit clicked - calling onToggleCompletion with habitId=[habitId], date=[timestamp]
BugFix: ViewModel Received Click - HabitId: [habitId], Date: [timestamp]
BugFix: Normalized dayStart: [normalizedTimestamp]
BugFix: Existing completion: [completion or null]
BugFix: [Creating new completion or Deleting existing completion] for habit [habitId]
BugFix: Toggle completion operation completed for habit [habitId]
BugFix: Completions updated: [count] total completions
BugFix: Processing completions for habit [habitId]: [count] completions
```

### For Measurable Habits:
```
BugFix: UI Clicked - Habit: [HabitName], DayStart: [timestamp], isScheduled: true
BugFix: Habit details - ID: [habitId], Type: NUMERICAL, HabitType: NUMERICAL
BugFix: NUMERICAL habit clicked - calling onMeasurableHabitClick with habitId=[habitId], date=[timestamp]
BugFix: showMeasurableHabitDialog called - HabitId: [habitId], Date: [timestamp]
BugFix: Retrieved habit: [habitName], Type: NUMERICAL, IsNumerical: true
BugFix: Updating dialog state - showDialog: true, habitName: [habitName]
BugFix: Dialog state updated - showDialog: true
BugFix: HomeScreen - Dialog state: isVisible=true, habitName=[habitName]
BugFix: NumericalInputDialog - isVisible: true, habitName: [habitName], unit: [unit]
BugFix: NumericalInputDialog - Rendering dialog
```

## Potential Root Causes

### Issue 1: Cross-Completion
1. **State Management Issue**: Completion state might be shared or incorrectly updated
2. **Database Query Issue**: Queries might be returning completions for wrong habits
3. **UI State Issue**: UI might be reacting to state changes incorrectly

### Issue 2: Measurable Dialog Not Showing
1. **Habit Type Detection**: Habit type might not be correctly identified
2. **Dialog State Propagation**: Dialog state might not be reaching the UI
3. **State Flow Combination**: The state combination might have timing issues

## Next Steps

1. Add the logging code to the app
2. Test both scenarios
3. Analyze the logs to identify where the flow breaks
4. Implement targeted fixes based on the findings
