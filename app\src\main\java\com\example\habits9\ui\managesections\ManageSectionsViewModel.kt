package com.example.habits9.ui.managesections

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.HabitSection
import com.example.habits9.data.HabitSectionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

data class ManageSectionsUiState(
    val sections: List<HabitSection> = emptyList(),
    val isLoading: Boolean = false,
    val showCreateDialog: Boolean = false,
    val showEditDialog: Boolean = false,
    val showDeleteDialog: Boolean = false,
    val editingSection: HabitSection? = null,
    val deletingSection: HabitSection? = null
)

@HiltViewModel
class ManageSectionsViewModel @Inject constructor(
    private val habitSectionRepository: HabitSectionRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ManageSectionsUiState())
    val uiState: StateFlow<ManageSectionsUiState> = _uiState.asStateFlow()

    init {
        loadSections()
    }

    private fun loadSections() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            habitSectionRepository.getAllHabitSections().collect { sections ->
                _uiState.value = _uiState.value.copy(
                    sections = sections,
                    isLoading = false
                )
            }
        }
    }

    fun showCreateDialog() {
        _uiState.value = _uiState.value.copy(showCreateDialog = true)
    }

    fun hideCreateDialog() {
        _uiState.value = _uiState.value.copy(showCreateDialog = false)
    }

    fun createSection(name: String, color: Int) {
        if (name.isBlank()) return

        viewModelScope.launch {
            val newSection = HabitSection(
                name = name.trim(),
                color = color,
                displayOrder = _uiState.value.sections.size // Add to end of list
            )
            
            habitSectionRepository.insertHabitSection(newSection)
            hideCreateDialog()
        }
    }

    fun showEditDialog(section: HabitSection) {
        _uiState.value = _uiState.value.copy(
            showEditDialog = true,
            editingSection = section
        )
    }

    fun hideEditDialog() {
        _uiState.value = _uiState.value.copy(
            showEditDialog = false,
            editingSection = null
        )
    }

    fun updateSection(name: String, color: Int) {
        val editingSection = _uiState.value.editingSection
        if (name.isBlank() || editingSection == null) return

        viewModelScope.launch {
            val updatedSection = editingSection.copy(
                name = name.trim(),
                color = color
            )
            
            habitSectionRepository.updateHabitSection(updatedSection)
            hideEditDialog()
        }
    }

    fun showDeleteDialog(section: HabitSection) {
        _uiState.value = _uiState.value.copy(
            showDeleteDialog = true,
            deletingSection = section
        )
    }

    fun hideDeleteDialog() {
        _uiState.value = _uiState.value.copy(
            showDeleteDialog = false,
            deletingSection = null
        )
    }

    fun deleteSection() {
        val deletingSection = _uiState.value.deletingSection
        if (deletingSection == null) return

        viewModelScope.launch {
            habitSectionRepository.deleteHabitSection(deletingSection)
            hideDeleteDialog()
        }
    }


    fun onSectionMoved(fromIndex: Int, toIndex: Int) {
        val currentSections = _uiState.value.sections.toMutableList()
        if (fromIndex < 0 || fromIndex >= currentSections.size || 
            toIndex < 0 || toIndex >= currentSections.size) return

        // Reorder the list in memory
        val movedSection = currentSections.removeAt(fromIndex)
        currentSections.add(toIndex, movedSection)

        // Re-assign displayOrder for all sections
        val updatedSections = currentSections.mapIndexed { index, section ->
            section.copy(displayOrder = index)
        }

        // Update UI state immediately for smooth UX
        _uiState.value = _uiState.value.copy(sections = updatedSections)

        // Persist to database in a transaction
        viewModelScope.launch {
            habitSectionRepository.updateHabitSections(updatedSections)
        }
    }
}