<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/contrast0"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context=".activities.habits.edit.EditHabitActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/colorPrimary"
            android:elevation="2dp"
            android:gravity="end"
            android:minHeight="?attr/actionBarSize"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:title="@string/create_habit"
            app:titleTextColor="@color/white">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSave"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="16dp"
                android:text="@string/save"
                android:textColor="@color/white"
                app:rippleColor="@color/white"
                app:strokeColor="@color/white" />
        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:background="?attr/contrast0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="8dp"
            android:paddingLeft="4dp"
            android:paddingRight="4dp">

            <!-- Title and color -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="false">

                <!-- Habit Title -->
                <FrameLayout
                    style="@style/FormOuterBox"
                    android:layout_width="0dp"
                    android:layout_weight="1">
                    <LinearLayout style="@style/FormInnerBox">
                        <TextView
                            style="@style/FormLabel"
                            android:text="@string/name" />
                        <EditText
                            android:id="@+id/nameInput"
                            style="@style/FormInput"
                            android:maxLines="1"
                            android:inputType="textCapSentences"
                            android:hint="@string/yes_or_no_short_example"
                            />
                    </LinearLayout>
                </FrameLayout>

                <!-- Habit Color -->
                <FrameLayout
                    style="@style/FormOuterBox"
                    android:layout_width="80dp"
                    android:layout_height="match_parent">
                    <LinearLayout style="@style/FormInnerBox">
                        <TextView
                            style="@style/FormLabel"
                            android:text="@string/color" />
                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/colorButton"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="16dp"
                            android:layout_marginTop="8dp"
                            android:layout_marginRight="16dp"
                            android:layout_marginBottom="8dp"
                            android:backgroundTint="#E23673" />

                    </LinearLayout>
                </FrameLayout>
            </LinearLayout>

            <!-- Habit Question -->
            <FrameLayout style="@style/FormOuterBox">
                <LinearLayout style="@style/FormInnerBox">
                    <TextView
                        style="@style/FormLabel"
                        android:text="@string/question" />
                    <EditText
                        android:id="@+id/questionInput"
                        style="@style/FormInput"
                        android:inputType="textCapSentences|textMultiLine"
                        android:hint="@string/example_question_boolean"
                         />
                </LinearLayout>
            </FrameLayout>

            <!-- Frequency -->
            <FrameLayout
                android:id="@+id/frequencyOuterBox"
                style="@style/FormOuterBox">
                <LinearLayout style="@style/FormInnerBox">
                    <TextView
                        style="@style/FormLabel"
                        android:text="@string/frequency" />
                    <TextView
                        style="@style/FormDropdown"
                        android:id="@+id/boolean_frequency_picker"
                        android:text="@string/every_day" />
                </LinearLayout>
            </FrameLayout>

            <!-- Target value, unit and frequency -->
            <FrameLayout
                android:id="@+id/unitOuterBox"
                style="@style/FormOuterBox">
                <LinearLayout style="@style/FormInnerBox">
                    <TextView
                        style="@style/FormLabel"
                        android:text="@string/unit" />
                    <EditText
                        style="@style/FormInput"
                        android:id="@+id/unitInput"
                        android:maxLines="1"
                        android:ems="10"
                        android:hint="@string/measurable_units_example"/>
                </LinearLayout>
            </FrameLayout>

            <LinearLayout
                android:id="@+id/targetOuterBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="false">
                <FrameLayout
                    style="@style/FormOuterBox"
                    android:layout_width="0dp"
                    android:layout_weight="1">
                    <LinearLayout style="@style/FormInnerBox">
                        <TextView
                            style="@style/FormLabel"
                            android:text="@string/target" />
                        <EditText
                            style="@style/FormInput"
                            android:id="@+id/targetInput"
                            android:maxLines="1"
                            android:inputType="numberDecimal"
                            android:hint="@string/example_target"/>
                    </LinearLayout>
                </FrameLayout>
                <FrameLayout
                    style="@style/FormOuterBox"
                    android:layout_width="0dp"
                    android:layout_weight="1">
                    <LinearLayout style="@style/FormInnerBox">
                        <TextView
                            style="@style/FormLabel"
                            android:text="@string/frequency" />
                        <TextView
                            style="@style/FormDropdown"
                            android:id="@+id/numericalFrequencyPicker"
                            android:text="@string/every_week"
                            android:textColor="?attr/contrast100"
                            />
                    </LinearLayout>
                </FrameLayout>
            </LinearLayout>

            <FrameLayout
                android:id="@+id/targetTypeOuterBox"
                style="@style/FormOuterBox">
                <LinearLayout style="@style/FormInnerBox">
                    <TextView
                        style="@style/FormLabel"
                        android:text="@string/target_type" />
                    <TextView
                        style="@style/FormDropdown"
                        android:id="@+id/targetTypePicker"
                        android:textColor="?attr/contrast100"
                        />
                </LinearLayout>
            </FrameLayout>


            <!-- Reminder -->
            <FrameLayout style="@style/FormOuterBox">
                <LinearLayout style="@style/FormInnerBox">
                    <TextView
                        style="@style/FormLabel"
                        android:text="@string/reminder" />

                    <TextView
                        style="@style/FormDropdown"
                        android:id="@+id/reminderTimePicker"
                        android:text="@string/reminder_off" />

                    <View
                        style="@style/FormDivider"
                        android:id="@+id/reminderDivider"/>

                    <TextView
                        style="@style/FormDropdown"
                        android:id="@+id/reminderDatePicker"
                        android:text="" />
                </LinearLayout>
            </FrameLayout>


            <!-- Notes -->
            <FrameLayout style="@style/FormOuterBox">
                <LinearLayout style="@style/FormInnerBox">
                    <TextView
                        style="@style/FormLabel"
                        android:text="@string/notes" />

                    <EditText
                        android:id="@+id/notesInput"
                        style="@style/FormInput"
                        android:inputType="textCapSentences|textMultiLine"
                        android:hint="@string/example_notes" />
                </LinearLayout>
            </FrameLayout>
        </LinearLayout>
    </ScrollView>

</LinearLayout>
