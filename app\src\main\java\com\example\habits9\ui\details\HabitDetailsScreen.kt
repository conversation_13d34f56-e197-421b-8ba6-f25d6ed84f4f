package com.example.habits9.ui.details

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.NotificationsOff
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// Design System Colors - Dark Theme (matching HomeScreen)
val DarkBackground = Color(0xFF121826) // background
val SurfaceVariantDark = Color(0xFF1A202C) // surface-variant
val TextPrimary = Color(0xFFE2E8F0) // text-primary
val TextSecondary = Color(0xFFA0AEC0) // text-secondary
val AccentPrimary = Color(0xFF81E6D9) // accent-primary
val DividerColor = Color(0xFF2D3748) // divider

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitDetailsScreen(
    onBackClick: () -> Unit = {}
) {
    Scaffold(
        containerColor = DarkBackground,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Test45",
                        color = TextPrimary,
                        fontSize = 14.sp,
                        fontFamily = FontFamily.Default,
                        fontWeight = FontWeight.SemiBold // display-small style
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = TextPrimary
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { /* TODO: Edit functionality */ }) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Edit",
                            tint = TextPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = DarkBackground,
                    titleContentColor = TextPrimary,
                    navigationIconContentColor = TextPrimary,
                    actionIconContentColor = TextPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            // Sub-Header Row
            SubHeaderRow()
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Overview Section
            OverviewSection()
        }
    }
}

@Composable
fun SubHeaderRow() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left Side: Calendar icon + "Every day"
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.DateRange,
                contentDescription = "Calendar",
                tint = TextSecondary,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Every day",
                color = TextSecondary,
                fontSize = 12.sp,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal // body-medium style
            )
        }
        
        // Right Side: Bell icon + "Off"
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.NotificationsOff,
                contentDescription = "Notifications Off",
                tint = TextSecondary,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Off",
                color = TextSecondary,
                fontSize = 12.sp,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal // body-medium style
            )
        }
    }
}

@Composable
fun OverviewSection() {
    Column {
        // Section Title
        Text(
            text = "Overview",
            color = TextPrimary,
            fontSize = 14.sp,
            fontFamily = FontFamily.Default,
            fontWeight = FontWeight.SemiBold // display-small style
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Metrics Row
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            MetricView(
                value = "19%",
                label = "Score",
                progress = 0.19f
            )
            
            MetricView(
                value = "+11%",
                label = "Month",
                progress = 0.11f
            )
            
            MetricView(
                value = "+19%",
                label = "Year",
                progress = 0.19f
            )
            
            MetricView(
                value = "7",
                label = "Total",
                progress = null // No progress ring for Total
            )
        }
    }
}

@Composable
fun MetricView(
    value: String,
    label: String,
    progress: Float? = null
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Progress Indicator (if provided)
        if (progress != null) {
            CircularProgressIndicator(
                progress = { progress },
                modifier = Modifier.size(24.dp),
                color = AccentPrimary,
                strokeWidth = 2.dp,
                trackColor = DividerColor
            )
        } else {
            // Empty space to maintain alignment
            Spacer(modifier = Modifier.size(24.dp))
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Value
        Text(
            text = value,
            color = TextPrimary,
            fontSize = 14.sp,
            fontFamily = FontFamily.Default,
            fontWeight = FontWeight.SemiBold // display-small style
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // Label
        Text(
            text = label,
            color = TextSecondary,
            fontSize = 10.sp,
            fontFamily = FontFamily.Monospace,
            fontWeight = FontWeight.Normal // label-small style
        )
    }
}