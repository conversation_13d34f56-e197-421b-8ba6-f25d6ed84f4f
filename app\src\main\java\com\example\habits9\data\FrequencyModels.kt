package com.example.habits9.data

import com.example.habits9.ui.createhabit.HabitFrequency

/**
 * Enhanced frequency system for customizable habit recurrence rules.
 * This replaces the simple numerator/denominator system with more flexible options.
 */

/**
 * Enum representing the primary frequency types
 */
enum class FrequencyType(val value: String) {
    DAILY("DAILY"),
    WEEKLY("WEEKLY"),
    MONTHLY("MONTHLY");
    
    companion object {
        fun fromString(value: String): FrequencyType {
            return when (value) {
                "DAILY" -> DAILY
                "WEEKLY" -> WEEKLY
                "MONTHLY" -> MONTHLY
                else -> DAILY // Default fallback
            }
        }
    }
}

/**
 * Days of the week for weekly frequency patterns
 */
enum class DayOfWeek(val value: Int, val shortName: String, val fullName: String) {
    MONDAY(1, "Mon", "Monday"),
    TUESDAY(2, "Tue", "Tuesday"),
    WEDNESDAY(3, "Wed", "Wednesday"),
    THURSDAY(4, "Thu", "Thursday"),
    FRIDAY(5, "Fri", "Friday"),
    SATURDAY(6, "Sat", "Saturday"),
    SUNDAY(7, "Sun", "Sunday");
    
    companion object {
        fun fromValue(value: Int): DayOfWeek? {
            return values().find { it.value == value }
        }
        
        fun fromString(daysString: String?): List<DayOfWeek> {
            if (daysString.isNullOrBlank()) return emptyList()
            return daysString.split(",")
                .mapNotNull { it.toIntOrNull() }
                .mapNotNull { fromValue(it) }
        }
        
        fun toString(days: List<DayOfWeek>): String {
            return days.map { it.value }.joinToString(",")
        }
    }
}

/**
 * Enhanced frequency configuration that maps to the new database columns
 */
data class EnhancedFrequency(
    val type: FrequencyType = FrequencyType.DAILY,
    val repeatsEvery: Int = 1,
    val daysOfWeek: List<DayOfWeek> = emptyList(),
    val dayOfMonth: Int? = null,
    val weekOfMonth: Int? = null,
    val dayOfWeekInMonth: DayOfWeek? = null
) {
    
    /**
     * Convert to database representation
     */
    fun toDatabaseValues(): DatabaseFrequency {
        return DatabaseFrequency(
            frequencyType = type.value,
            repeatsEvery = repeatsEvery,
            daysOfWeek = daysOfWeek.map { it.value.toLong() },
            dayOfMonth = dayOfMonth,
            weekOfMonth = weekOfMonth,
            dayOfWeekInMonth = dayOfWeekInMonth?.value
        )
    }
    
    /**
     * Generate human-readable display string
     */
    fun toDisplayString(): String {
        return when (type) {
            FrequencyType.DAILY -> {
                if (repeatsEvery == 1) {
                    "Every day"
                } else {
                    "Every $repeatsEvery days"
                }
            }
            FrequencyType.WEEKLY -> {
                val daysText = when {
                    daysOfWeek.isEmpty() -> "any day"
                    daysOfWeek.size == 7 -> "every day"
                    daysOfWeek.size == 1 -> "on ${daysOfWeek.first().fullName}"
                    daysOfWeek.size <= 3 -> "on ${daysOfWeek.joinToString(", ") { it.shortName }}"
                    else -> "on ${daysOfWeek.size} days"
                }

                if (repeatsEvery == 1) {
                    "Weekly $daysText"
                } else {
                    "Every $repeatsEvery weeks $daysText"
                }
            }
            FrequencyType.MONTHLY -> {
                val monthText = if (repeatsEvery == 1) "month" else "$repeatsEvery months"

                when {
                    dayOfMonth != null -> "Every $monthText on the ${dayOfMonth}${getOrdinalSuffix(dayOfMonth)}"
                    weekOfMonth != null && dayOfWeekInMonth != null -> {
                        val weekText = when (weekOfMonth) {
                            1 -> "1st"
                            2 -> "2nd"
                            3 -> "3rd"
                            4 -> "4th"
                            -1 -> "last"
                            else -> "${weekOfMonth}th"
                        }
                        "Every $monthText on the $weekText ${dayOfWeekInMonth.fullName}"
                    }
                    else -> "Every $monthText"
                }
            }
        }
    }

    /**
     * Generate a short display string for compact UI elements
     */
    fun toShortDisplayString(): String {
        return when (type) {
            FrequencyType.DAILY -> {
                if (repeatsEvery == 1) "Daily" else "Every ${repeatsEvery}d"
            }
            FrequencyType.WEEKLY -> {
                val prefix = if (repeatsEvery == 1) "Weekly" else "${repeatsEvery}w"
                when {
                    daysOfWeek.isEmpty() -> prefix
                    daysOfWeek.size == 7 -> "Daily"
                    daysOfWeek.size == 1 -> "${daysOfWeek.first().shortName}"
                    else -> "$prefix (${daysOfWeek.size}d)"
                }
            }
            FrequencyType.MONTHLY -> {
                if (repeatsEvery == 1) "Monthly" else "${repeatsEvery}m"
            }
        }
    }

    /**
     * Generate a detailed description for settings or info displays
     */
    fun toDetailedDescription(): String {
        return when (type) {
            FrequencyType.DAILY -> {
                if (repeatsEvery == 1) {
                    "This habit should be completed every day."
                } else {
                    "This habit should be completed every $repeatsEvery days."
                }
            }
            FrequencyType.WEEKLY -> {
                val daysText = when {
                    daysOfWeek.isEmpty() -> "any day of the week"
                    daysOfWeek.size == 7 -> "every day of the week"
                    daysOfWeek.size == 1 -> "every ${daysOfWeek.first().fullName}"
                    else -> "on ${daysOfWeek.joinToString(", ") { it.fullName }}"
                }

                val intervalText = if (repeatsEvery == 1) "week" else "$repeatsEvery weeks"
                "This habit should be completed $daysText, repeating every $intervalText."
            }
            FrequencyType.MONTHLY -> {
                val intervalText = if (repeatsEvery == 1) "month" else "$repeatsEvery months"

                when {
                    dayOfMonth != null -> {
                        "This habit should be completed on the ${dayOfMonth}${getOrdinalSuffix(dayOfMonth)} day of every $intervalText."
                    }
                    weekOfMonth != null && dayOfWeekInMonth != null -> {
                        val weekText = when (weekOfMonth) {
                            1 -> "first"
                            2 -> "second"
                            3 -> "third"
                            4 -> "fourth"
                            -1 -> "last"
                            else -> "${weekOfMonth}th"
                        }
                        "This habit should be completed on the $weekText ${dayOfWeekInMonth.fullName} of every $intervalText."
                    }
                    else -> "This habit should be completed once every $intervalText."
                }
            }
        }
    }
    
    private fun getOrdinalSuffix(number: Int): String {
        return when {
            number in 11..13 -> "th"
            number % 10 == 1 -> "st"
            number % 10 == 2 -> "nd"
            number % 10 == 3 -> "rd"
            else -> "th"
        }
    }
    
    /**
     * Validate the frequency configuration
     */
    fun isValid(): Boolean {
        if (repeatsEvery < 1) return false

        return when (type) {
            FrequencyType.DAILY -> true
            FrequencyType.WEEKLY -> {
                // Weekly habits can have empty days (means any day)
                daysOfWeek.all { it in DayOfWeek.values() }
            }
            FrequencyType.MONTHLY -> {
                when {
                    dayOfMonth != null -> dayOfMonth in 1..31
                    weekOfMonth != null && dayOfWeekInMonth != null -> {
                        weekOfMonth in listOf(-1, 1, 2, 3, 4) && dayOfWeekInMonth in DayOfWeek.values()
                    }
                    else -> true // Basic monthly is valid
                }
            }
        }
    }

    /**
     * Get validation error message if invalid
     */
    fun getValidationError(): String? {
        if (repeatsEvery < 1) return "Repeat interval must be at least 1"

        return when (type) {
            FrequencyType.DAILY -> null
            FrequencyType.WEEKLY -> {
                if (daysOfWeek.any { it !in DayOfWeek.values() }) {
                    "Invalid days of week selected"
                } else null
            }
            FrequencyType.MONTHLY -> {
                when {
                    dayOfMonth != null && dayOfMonth !in 1..31 -> "Day of month must be between 1 and 31"
                    weekOfMonth != null && weekOfMonth !in listOf(-1, 1, 2, 3, 4) -> "Week of month must be 1-4 or -1 (last)"
                    else -> null
                }
            }
        }
    }

    companion object {
        /**
         * Create from database values
         */
        fun fromDatabaseValues(
            frequencyType: String,
            repeatsEvery: Int,
            daysOfWeek: List<Long>,
            dayOfMonth: Int?,
            weekOfMonth: Int?,
            dayOfWeekInMonth: Int?
        ): EnhancedFrequency {
            return EnhancedFrequency(
                type = FrequencyType.fromString(frequencyType),
                repeatsEvery = repeatsEvery,
                daysOfWeek = daysOfWeek.mapNotNull { DayOfWeek.fromValue(it.toInt()) },
                dayOfMonth = dayOfMonth,
                weekOfMonth = weekOfMonth,
                dayOfWeekInMonth = dayOfWeekInMonth?.let { DayOfWeek.fromValue(it) }
            )
        }

        /**
         * Common frequency presets
         */
        val DAILY = EnhancedFrequency(FrequencyType.DAILY, 1)
        val EVERY_OTHER_DAY = EnhancedFrequency(FrequencyType.DAILY, 2)
        val WEEKDAYS = EnhancedFrequency(
            FrequencyType.WEEKLY,
            1,
            listOf(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY)
        )
        val WEEKENDS = EnhancedFrequency(
            FrequencyType.WEEKLY,
            1,
            listOf(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
        )
        val WEEKLY = EnhancedFrequency(FrequencyType.WEEKLY, 1, listOf(DayOfWeek.MONDAY))
        val MONTHLY = EnhancedFrequency(FrequencyType.MONTHLY, 1, dayOfMonth = 1)
    }
}

/**
 * Data class representing the database columns for frequency
 */
data class DatabaseFrequency(
    val frequencyType: String,
    val repeatsEvery: Int,
    val daysOfWeek: List<Long>,
    val dayOfMonth: Int?,
    val weekOfMonth: Int?,
    val dayOfWeekInMonth: Int?
)

/**
 * Backward compatibility with the old HabitFrequency system
 */
fun EnhancedFrequency.toLegacyFrequency(): HabitFrequency {
    return when (type) {
        FrequencyType.DAILY -> HabitFrequency(1, repeatsEvery)
        FrequencyType.WEEKLY -> {
            val daysCount = if (daysOfWeek.isEmpty()) 1 else daysOfWeek.size
            HabitFrequency(daysCount, 7 * repeatsEvery)
        }
        FrequencyType.MONTHLY -> HabitFrequency(1, 30 * repeatsEvery)
    }
}

/**
 * Convert legacy HabitFrequency to EnhancedFrequency (best effort)
 */
fun HabitFrequency.toEnhancedFrequency(): EnhancedFrequency {
    return when {
        denominator == 1 -> EnhancedFrequency(FrequencyType.DAILY, numerator)
        denominator > 1 && numerator == 1 -> EnhancedFrequency(FrequencyType.DAILY, denominator)
        denominator == 7 -> EnhancedFrequency(FrequencyType.WEEKLY, 1, emptyList()) // Generic weekly
        denominator == 30 -> EnhancedFrequency(FrequencyType.MONTHLY, numerator)
        denominator % 7 == 0 -> EnhancedFrequency(FrequencyType.WEEKLY, denominator / 7, emptyList())
        else -> EnhancedFrequency(FrequencyType.DAILY, 1) // Fallback
    }
}
