<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="android.widget.LinearLayout"
    tools:orientation="vertical"
    tools:layout_width="match_parent"
    tools:layout_height="wrap_content">

    <TextView
        android:id="@+id/questionLabel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:textColor="?attr/contrast60"
        android:textSize="@dimen/regularTextSize"
        tools:text="Have you worked out today?" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="2dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:visibility="visible">

        <TextView
            android:id="@+id/targetIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/contrast60"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/targetText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="?attr/contrast60"
            android:textSize="@dimen/smallTextSize"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="16dp"
            android:maxEms="7"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/frequencyIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fa_calendar"
            android:textColor="?attr/contrast60"
            android:textSize="@dimen/smallTextSize" />

        <TextView
            android:id="@+id/frequencyLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/every_day"
            android:textColor="?attr/contrast60"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="16dp"
            android:textSize="@dimen/smallTextSize" />

        <TextView
            android:id="@+id/reminderIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fa_bell_o"
            android:textColor="?attr/contrast60"
            android:textSize="@dimen/smallTextSize" />

        <TextView
            android:id="@+id/reminderLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="1dp"
            android:textColor="?attr/contrast60"
            android:text=""
            android:layout_marginStart="4dp"
            android:textSize="@dimen/smallTextSize" />

    </LinearLayout>
</merge>