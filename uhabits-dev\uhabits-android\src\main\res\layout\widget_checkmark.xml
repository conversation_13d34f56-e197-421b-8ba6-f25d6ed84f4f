<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<LinearLayout
    android:id="@+id/frame"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:habit="http://isoron.org/android"
    android:gravity="center"
    android:orientation="vertical">

    <org.isoron.uhabits.activities.common.views.RingView
        android:id="@+id/scoreRing"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.9"
        habit:thickness="2"
        habit:textSize="16"
        habit:enableFontAwesome="true"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp"/>

    <TextView
        android:id="@+id/label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="0.1"
        android:textSize="12sp"
        android:textColor="@color/white"
        android:layout_marginLeft="6dp"
        android:layout_marginRight="6dp"
        android:gravity="center"
        android:scrollHorizontally="true"
        android:ellipsize="end"
        android:maxLines="2"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp"
        android:fontFamily="sans-serif-condensed"
        android:breakStrategy="balanced" />

</LinearLayout>