package com.example.habits9.ui.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {
    
    /**
     * StateFlow for the first day of week preference
     */
    val firstDayOfWeek: StateFlow<String> = userPreferencesRepository.firstDayOfWeek
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = "SUNDAY"
        )
    
    /**
     * Update the first day of week preference
     * @param dayOfWeek String - "SUNDAY" or "MONDAY"
     */
    fun updateFirstDayOfWeek(dayOfWeek: String) {
        viewModelScope.launch {
            userPreferencesRepository.updateFirstDayOfWeek(dayOfWeek)
        }
    }
}