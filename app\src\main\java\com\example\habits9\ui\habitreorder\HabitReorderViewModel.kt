package com.example.habits9.ui.habitreorder

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitSortType
import com.example.habits9.data.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

data class HabitReorderUiState(
    val habits: List<Habit> = emptyList(),
    val isLoading: Boolean = false
)

@HiltViewModel
class HabitReorderViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _reorderedHabits = MutableStateFlow<List<Habit>>(emptyList())

    val uiState: StateFlow<HabitReorderUiState> = combine(
        habitRepository.getAllHabits(),
        _reorderedHabits
    ) { allHabits, reorderedHabits ->
        val habitsToShow = if (reorderedHabits.isNotEmpty()) {
            reorderedHabits
        } else {
            // Sort by current position initially
            allHabits.sortedBy { it.position }
        }
        
        HabitReorderUiState(
            habits = habitsToShow,
            isLoading = false
        )
    }.stateIn(
        scope = viewModelScope,
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
        initialValue = HabitReorderUiState(isLoading = true)
    )

    init {
        // Set sort type to custom order when entering this screen
        viewModelScope.launch {
            userPreferencesRepository.updateHabitSortType(HabitSortType.CUSTOM_ORDER)
        }
    }

    /**
     * Move a habit from one position to another
     */
    fun moveHabit(fromIndex: Int, toIndex: Int) {
        val currentHabits = _reorderedHabits.value.ifEmpty { 
            uiState.value.habits 
        }
        
        if (fromIndex < 0 || fromIndex >= currentHabits.size || 
            toIndex < 0 || toIndex >= currentHabits.size || 
            fromIndex == toIndex) {
            return
        }

        val mutableList = currentHabits.toMutableList()
        val item = mutableList.removeAt(fromIndex)
        mutableList.add(toIndex, item)
        
        _reorderedHabits.value = mutableList
    }

    /**
     * Save the current order to preferences and update habit positions
     */
    fun saveOrder() {
        viewModelScope.launch {
            val currentHabits = _reorderedHabits.value.ifEmpty {
                uiState.value.habits
            }

            if (currentHabits.isNotEmpty()) {
                // Save custom order to preferences
                val habitUuids = currentHabits.map { it.uuid }
                userPreferencesRepository.updateCustomHabitOrder(habitUuids)

                // Update habit positions in the database
                val updatedHabits = currentHabits.mapIndexed { index, habit ->
                    habit.copy(position = index)
                }

                // Update all habits with new positions
                updatedHabits.forEach { habit ->
                    habitRepository.updateHabit(habit)
                }

                // Add a small delay to ensure database operations complete
                delay(100)
            }
        }
    }

    /**
     * Clear the reordered habits state to prevent stale state issues
     */
    fun clearReorderedState() {
        _reorderedHabits.value = emptyList()
    }
}
