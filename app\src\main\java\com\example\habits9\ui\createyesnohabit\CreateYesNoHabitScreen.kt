package com.example.habits9.ui.createyesnohabit

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.habits9.ui.createhabit.CreateHabitViewModel
import com.example.habits9.ui.createhabit.HabitFrequency
import com.example.habits9.ui.createhabit.ReminderState
import com.example.habits9.data.EnhancedFrequency
import com.example.habits9.ui.components.EnhancedFrequencyPickerDialog
import java.time.DayOfWeek
import java.time.LocalTime

// Design System Colors - Dark Theme
private val DarkBackground = Color(0xFF121826)
private val SurfaceVariantDark = Color(0xFF1A202C)
private val TextPrimary = Color(0xFFE2E8F0)
private val TextSecondary = Color(0xFFA0AEC0)
private val AccentPrimary = Color(0xFF81E6D9)
private val DividerColor = Color(0xFF2D3748)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateYesNoHabitScreen(
    onBackClick: () -> Unit = {},
    onSaveClick: () -> Unit = {},
    viewModel: CreateHabitViewModel = hiltViewModel()
) {
    var name by remember { mutableStateOf("") }
    var question by remember { mutableStateOf("") }
    var notes by remember { mutableStateOf("") }
    var showFrequencyDialog by remember { mutableStateOf(false) }
    var showErrorSnackbar by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    
    val uiState by viewModel.uiState.collectAsState()
    
    // Handle save button click
    val handleSave = {
        viewModel.saveHabit(
            name = name,
            question = question,
            notes = notes,
            habitType = com.example.habits9.data.HabitType.YES_NO,
            onSuccess = onSaveClick,
            onError = { error ->
                errorMessage = error
                showErrorSnackbar = true
            }
        )
    }

    Scaffold(
        containerColor = DarkBackground,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Create habit",
                        color = TextPrimary,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = TextPrimary
                        )
                    }
                },
                actions = {
                    TextButton(onClick = handleSave) {
                        Text(
                            text = "SAVE",
                            color = AccentPrimary,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = SurfaceVariantDark
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.height(24.dp))

            // Habit Section Selector
            OutlinedTextField(
                value = uiState.selectedSection?.name ?: "Select Section",
                onValueChange = { },
                label = { Text("Habit Section", color = TextSecondary) },
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { viewModel.showSectionSelector() },
                enabled = false,
                trailingIcon = {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "Select habit section",
                        tint = TextSecondary
                    )
                },
                colors = OutlinedTextFieldDefaults.colors(
                    disabledTextColor = TextPrimary,
                    disabledBorderColor = DividerColor,
                    disabledLabelColor = TextSecondary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Name Field with Color Selector
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Name", color = TextSecondary) },
                    placeholder = { Text("e.g. Exercise", color = TextSecondary) },
                    modifier = Modifier.weight(1f),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary,
                        focusedBorderColor = AccentPrimary,
                        unfocusedBorderColor = DividerColor,
                        cursorColor = AccentPrimary
                    )
                )
                Spacer(modifier = Modifier.width(12.dp))
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(Color(uiState.selectedColor), CircleShape)
                        .clickable { /* Color picker would go here */ }
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Question Field
            OutlinedTextField(
                value = question,
                onValueChange = { question = it },
                label = { Text("Question", color = TextSecondary) },
                placeholder = { Text("e.g. Did you exercise today?", color = TextSecondary) },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = TextPrimary,
                    unfocusedTextColor = TextPrimary,
                    focusedBorderColor = AccentPrimary,
                    unfocusedBorderColor = DividerColor,
                    cursorColor = AccentPrimary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Frequency Field
            OutlinedTextField(
                value = uiState.enhancedFrequency.toDisplayString(),
                onValueChange = { },
                label = { Text("Frequency", color = TextSecondary) },
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { showFrequencyDialog = true },
                enabled = false,
                trailingIcon = {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "Select frequency",
                        tint = TextSecondary
                    )
                },
                colors = OutlinedTextFieldDefaults.colors(
                    disabledTextColor = TextPrimary,
                    disabledBorderColor = DividerColor,
                    disabledLabelColor = TextSecondary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Reminder Field
            OutlinedTextField(
                value = uiState.reminderState.toDisplayString(),
                onValueChange = { },
                label = { Text("Reminder", color = TextSecondary) },
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { viewModel.showReminderDialog() },
                enabled = false,
                trailingIcon = {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "Select reminder",
                        tint = TextSecondary
                    )
                },
                colors = OutlinedTextFieldDefaults.colors(
                    disabledTextColor = TextPrimary,
                    disabledBorderColor = DividerColor,
                    disabledLabelColor = TextSecondary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Notes Field
            OutlinedTextField(
                value = notes,
                onValueChange = { notes = it },
                label = { Text("Notes", color = TextSecondary) },
                placeholder = { Text("(Optional)", color = TextSecondary) },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = TextPrimary,
                    unfocusedTextColor = TextPrimary,
                    focusedBorderColor = AccentPrimary,
                    unfocusedBorderColor = DividerColor,
                    cursorColor = AccentPrimary
                )
            )

            Spacer(modifier = Modifier.height(24.dp))
        }
    }

    // Frequency Dialog
    if (showFrequencyDialog) {
        EnhancedFrequencyPickerDialog(
            currentFrequency = uiState.enhancedFrequency,
            onFrequencySelected = { frequency ->
                viewModel.updateEnhancedFrequency(frequency)
                showFrequencyDialog = false
            },
            onDismiss = { showFrequencyDialog = false }
        )
    }
    
    // Section Selector Dialog
    if (uiState.showSectionSelector) {
        SectionSelectorDialog(
            sections = uiState.availableSections,
            onSectionSelected = { section ->
                viewModel.selectSection(section)
            },
            onDismiss = {
                viewModel.hideSectionSelector()
            }
        )
    }
    
    // Reminder Dialog
    if (uiState.showReminderDialog) {
        ReminderDialog(
            currentReminderState = uiState.reminderState,
            onReminderSet = { reminderState ->
                viewModel.updateReminder(reminderState)
            },
            onDismiss = {
                viewModel.hideReminderDialog()
            }
        )
    }
    
    // Error Snackbar
    if (showErrorSnackbar) {
        LaunchedEffect(showErrorSnackbar) {
            kotlinx.coroutines.delay(3000)
            showErrorSnackbar = false
        }
    }
}

@Composable
private fun ReminderDialog(
    currentReminderState: ReminderState,
    onReminderSet: (ReminderState) -> Unit,
    onDismiss: () -> Unit
) {
    var isEnabled by remember { mutableStateOf(currentReminderState.isEnabled) }
    var selectedTime by remember { mutableStateOf(currentReminderState.time) }
    var selectedDays by remember { mutableStateOf(currentReminderState.selectedDays) }
    var showTimePicker by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Set Reminder",
                color = TextPrimary,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium
            )
        },
        text = {
            Column {
                // Enable/Disable Switch
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Enable Reminder",
                        color = TextPrimary,
                        fontSize = 16.sp
                    )
                    Switch(
                        checked = isEnabled,
                        onCheckedChange = { isEnabled = it },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = AccentPrimary,
                            checkedTrackColor = AccentPrimary.copy(alpha = 0.5f),
                            uncheckedThumbColor = TextSecondary,
                            uncheckedTrackColor = DividerColor
                        )
                    )
                }

                if (isEnabled) {
                    Spacer(modifier = Modifier.height(16.dp))

                    // Time Picker
                    OutlinedTextField(
                        value = String.format("%02d:%02d", selectedTime.hour, selectedTime.minute),
                        onValueChange = { },
                        label = { Text("Time", color = TextSecondary) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { showTimePicker = true },
                        enabled = false,
                        trailingIcon = {
                            Icon(
                                imageVector = Icons.Default.KeyboardArrowDown,
                                contentDescription = "Select time",
                                tint = TextSecondary
                            )
                        },
                        colors = OutlinedTextFieldDefaults.colors(
                            disabledTextColor = TextPrimary,
                            disabledBorderColor = DividerColor,
                            disabledLabelColor = TextSecondary
                        )
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Day Selector
                    Text(
                        text = "Days",
                        color = TextSecondary,
                        fontSize = 14.sp
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        val daysOfWeek = listOf(
                            DayOfWeek.SUNDAY to "S",
                            DayOfWeek.MONDAY to "M", 
                            DayOfWeek.TUESDAY to "T",
                            DayOfWeek.WEDNESDAY to "W",
                            DayOfWeek.THURSDAY to "T",
                            DayOfWeek.FRIDAY to "F",
                            DayOfWeek.SATURDAY to "S"
                        )
                        
                        daysOfWeek.forEach { (day, label) ->
                            val isSelected = selectedDays.contains(day)
                            Box(
                                modifier = Modifier
                                    .size(40.dp)
                                    .background(
                                        if (isSelected) AccentPrimary else DividerColor,
                                        CircleShape
                                    )
                                    .clickable {
                                        selectedDays = if (isSelected) {
                                            selectedDays - day
                                        } else {
                                            selectedDays + day
                                        }
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = label,
                                    color = if (isSelected) DarkBackground else TextSecondary,
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val newReminderState = if (isEnabled && selectedDays.isNotEmpty()) {
                        ReminderState(
                            isEnabled = true,
                            time = selectedTime,
                            selectedDays = selectedDays
                        )
                    } else {
                        ReminderState(isEnabled = false)
                    }
                    onReminderSet(newReminderState)
                }
            ) {
                Text("OK", color = AccentPrimary)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel", color = TextSecondary)
            }
        },
        containerColor = SurfaceVariantDark
    )

    // Time Picker Dialog
    if (showTimePicker) {
        TimePickerDialog(
            currentTime = selectedTime,
            onTimeSelected = { time ->
                selectedTime = time
                showTimePicker = false
            },
            onDismiss = { showTimePicker = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TimePickerDialog(
    currentTime: LocalTime,
    onTimeSelected: (LocalTime) -> Unit,
    onDismiss: () -> Unit
) {
    val timePickerState = rememberTimePickerState(
        initialHour = currentTime.hour,
        initialMinute = currentTime.minute,
        is24Hour = true
    )

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Select Time",
                color = TextPrimary,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium
            )
        },
        text = {
            TimePicker(
                state = timePickerState,
                colors = TimePickerDefaults.colors(
                    clockDialColor = SurfaceVariantDark,
                    selectorColor = AccentPrimary,
                    containerColor = SurfaceVariantDark,
                    periodSelectorBorderColor = DividerColor,
                    clockDialSelectedContentColor = DarkBackground,
                    clockDialUnselectedContentColor = TextSecondary,
                    periodSelectorSelectedContainerColor = AccentPrimary,
                    periodSelectorUnselectedContainerColor = DividerColor,
                    periodSelectorSelectedContentColor = DarkBackground,
                    periodSelectorUnselectedContentColor = TextSecondary,
                    timeSelectorSelectedContainerColor = AccentPrimary,
                    timeSelectorUnselectedContainerColor = DividerColor,
                    timeSelectorSelectedContentColor = DarkBackground,
                    timeSelectorUnselectedContentColor = TextSecondary
                )
            )
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onTimeSelected(LocalTime.of(timePickerState.hour, timePickerState.minute))
                }
            ) {
                Text("OK", color = AccentPrimary)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel", color = TextSecondary)
            }
        },
        containerColor = SurfaceVariantDark
    )
}

@Composable
private fun SectionSelectorDialog(
    sections: List<com.example.habits9.data.HabitSection>,
    onSectionSelected: (com.example.habits9.data.HabitSection) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Select Habit Section",
                color = TextPrimary,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium
            )
        },
        text = {
            Column {
                sections.forEach { section ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onSectionSelected(section) }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(24.dp)
                                .background(Color(section.color), CircleShape)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = section.name,
                            color = TextPrimary,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(
                    text = "Cancel",
                    color = AccentPrimary
                )
            }
        },
        containerColor = SurfaceVariantDark,
        titleContentColor = TextPrimary,
        textContentColor = TextPrimary
    )
}