# Prompt: <PERSON><PERSON> and <PERSON>hance Custom Habit Reordering

## 1. Objective & Context

This task is to fix critical bugs in the "Custom Order" drag-and-drop functionality, as shown in `49.jpg`. The goal is to create a smooth, intuitive, and persistent reordering experience.

Currently, the feature suffers from two major issues:
1.  **Poor UX:** The drag-and-drop interaction is not smooth, and there is no clear visual indicator of where a habit will be dropped, leading to user confusion.
2.  **No Persistence:** After reordering habits and returning to the main screen (`48.jpg`), the new order is lost, and the list reverts to its previous state.

**Root Cause Analysis:**
- The poor UX likely stems from an incomplete `ItemTouchHelper` implementation, which fails to provide adequate visual feedback (elevation, background changes, and item displacement) during the drag gesture.
- The lack of persistence is because the reordered list state exists only in memory within the "Reorder Habits" screen. It is never saved back to our source of truth, Firestore. When the user navigates back, the `ViewModel` re-fetches the original, unsorted data from the database.

## 2. Detailed Implementation Plan

### Part 1: Improve Drag-and-Drop Visual Feedback

The interaction on the "Reorder Habits" screen (`49.jpg`) must be polished to feel intuitive and responsive.

**Task 1.1: Implement "Lift" on Drag**
- When a user long-presses a habit to start dragging, it must visually lift off the list.
- **Referencing `style.md`:** Apply a subtle elevation shadow and change the item's background color to the `surface-variant` token to distinguish it from the rest of the list. This change should be animated smoothly.

**Task 1.2: Implement Smooth Item Following**
- Ensure the dragged item view tracks the user's finger movement smoothly and accurately across the screen.

**Task 1.3: Provide a Clear Drop Target Indicator**
- As a user drags a habit over the list, the other items in the list must animate out of the way to create an empty space, clearly indicating where the habit will be dropped if released.

**Task 1.4: Implement "Drop" Animation**
- When the user releases the dragged habit, it should animate smoothly into its new position in the list.
- The background color and elevation should animate back to their original state, as defined in `style.md`.

### Part 2: Persist the Custom Order to Firestore

To make the new order permanent, we must store it in the database.

**Task 2.1: Update the Habit Data Model**
- Add a new field to the `Habit` data model (and its corresponding Firestore document). Name this field `customOrderIndex` and use a `Double` or `Long` as its type. This field will store the numerical position of each habit in the custom-sorted list.

**Task 2.2: Implement Save Logic**
- In the "Reorder Habits" screen, after a drag-and-drop operation is completed (i.e., the user drops a habit in a new position):
    1. Get the final, reordered list of habits from the `RecyclerView`'s adapter.
    2. Iterate through this new list and update the `customOrderIndex` for every habit based on its new position (e.g., the first habit gets index 0, the second gets 1, and so on).
    3. Use a **Firestore batch write** to update all the modified `Habit` documents in a single, atomic operation. This is critical for data consistency.

**Task 2.3: Update Data Fetching Logic**
- In the `ViewModel` responsible for loading habits for the main screen (`48.jpg`):
    1. When the user has selected the "Custom order" sort option, modify the Firestore query.
    2. Add an `orderBy("customOrderIndex")` clause to the query to ensure habits are fetched and displayed in the user-defined order.

## 3. Verification Plan

Please follow these steps meticulously to confirm the fix.

1.  Navigate from the main screen (`48.jpg`) to the "Reorder Habits" screen (`49.jpg`).
2.  Long-press any habit in the list.
3.  **Verify:** The habit item visually lifts, its background color changes, and a shadow appears.
4.  Drag the habit up and down the list.
5.  **Verify:** The item follows your finger smoothly. As you drag it over other habits, they move aside to clearly show a gap where the item can be dropped.
6.  Drop the habit into a new position (e.g., move the last item to the top).
7.  **Verify:** The habit animates cleanly into its new spot, and its appearance returns to normal.
8.  Reorder one or two more habits.
9.  Press the back button to return to the main habits screen.
10. **Verify (CRITICAL):** The habits on the main screen are now displayed in the exact new order you just created.
11. Completely close and restart the application.
12. **Verify:** The custom order is still correctly applied on the main screen.
13. On the main screen, switch the sort option to "Sort by name," then switch it back to "Custom order."
14. **Verify:** The list correctly reverts to your saved custom order.

## 4. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation. 
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.

---

By adhering to these principles and mandatory practices, we ensure clarity, precision, and high-quality development that scales well over time.
