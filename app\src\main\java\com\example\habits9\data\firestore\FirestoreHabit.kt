package com.example.habits9.data.firestore

import java.util.UUID

/**
 * Firestore data model for habits.
 * This is a simple POJO without Room annotations for Firestore serialization/deserialization.
 */
data class FirestoreHabit(
    val id: String = "", // Firestore document ID
    val name: String = "",
    val description: String = "",
    val creationDate: Long = System.currentTimeMillis(),
    val createdAt: Long = 0L, // Fix for Firestore deserialization crash
    val currentStreak: Int = 0,
    val completionDatesJson: String = "{}",
    val uuid: String = UUID.randomUUID().toString().replace("-", ""),
    val isArchived: Boolean = false,
    val position: Int = 0,
    val color: Int = 8,
    val sectionId: String = "", // ID of the section this habit belongs to
    val type: String = "YES_NO", // HabitType as string
    val targetType: Int = 0, // NumericalHabitType.AT_LEAST.value
    val targetValue: Double = 0.0,
    val unit: String = "",

    // Frequency-related fields
    val frequencyType: String = "DAILY",
    val repeatsEvery: Int = 1,
    val daysOfWeek: List<Long> = emptyList(),
    val dayOfMonth: Int? = null,
    val weekOfMonth: Int? = null,
    val dayOfWeekInMonth: Int? = null
) {
    // No-argument constructor required by Firestore
    constructor() : this(
        id = "",
        name = "",
        description = "",
        creationDate = System.currentTimeMillis(),
        createdAt = 0L,
        currentStreak = 0,
        completionDatesJson = "{}",
        uuid = UUID.randomUUID().toString().replace("-", ""),
        isArchived = false,
        position = 0,
        color = 8,
        sectionId = "",
        type = "YES_NO",
        targetType = 0,
        targetValue = 0.0,
        unit = "",
        frequencyType = "DAILY",
        repeatsEvery = 1,
        daysOfWeek = emptyList(),
        dayOfMonth = null,
        weekOfMonth = null,
        dayOfWeekInMonth = null
    )
}
