package com.example.habits9.data

import java.time.LocalDate
import java.util.UUID

data class Habit(
    val id: Long = 0,
    val name: String,
    val description: String = "",
    val creationDate: Long = System.currentTimeMillis(), // Using timestamp
    val currentStreak: Int = 0,
    val completionDatesJson: String = "{}", // Using JSON string instead of Map
    val uuid: String = UUID.randomUUID().toString().replace("-", ""),
    val isArchived: Boolean = false,
    val position: Int = 0,
    val color: Int = 8, // Default color index
    val sectionId: String = "", // ID of the section this habit belongs to (empty = no section)
    val type: String = "YES_NO", // Habit type (YES_NO or NUMERICAL)
    val targetType: Int = NumericalHabitType.AT_LEAST.value, // For numerical habits
    val targetValue: Double = 0.0, // Target value for numerical habits
    val unit: String = "", // Unit of measurement for numerical habits (e.g., "glasses", "km")

    // New frequency-related columns for customizable recurrence rules
    val frequencyType: String = "DAILY", // Primary type of frequency (e.g., "DAILY", "WEEKLY", "MONTHLY")
    val repeatsEvery: Int = 1, // Interval (e.g., repeats every 2 weeks). Defaults to 1
    val daysOfWeek: List<Long> = emptyList(), // List of day numbers for weekly habits (e.g., [1,4,6] for Mon, Thu, Sat)
    val dayOfMonth: Int? = null, // The day for monthly habits (e.g., the 15th)
    val weekOfMonth: Int? = null, // The week number for advanced monthly habits (e.g., the 3rd week)
    val dayOfWeekInMonth: Int? = null // The day of the week for advanced monthly habits (e.g., 2 for Tuesday)
) {
    val habitType: HabitType
        get() = when (type) {
            "YES_NO" -> HabitType.YES_NO
            "NUMERICAL" -> HabitType.NUMERICAL
            else -> HabitType.YES_NO // Default fallback
        }

    // Type-safe enum accessor (recommended for new code)
    val typeEnum: HabitType
        get() = habitType

    val numericalHabitType: NumericalHabitType
        get() = NumericalHabitType.fromInt(targetType)

    val isNumerical: Boolean
        get() = habitType == HabitType.NUMERICAL

    // Computed property to maintain type safety while keeping Firestore compatibility
    val frequencyTypeEnum: FrequencyType
        get() = FrequencyType.fromString(frequencyType)
}
